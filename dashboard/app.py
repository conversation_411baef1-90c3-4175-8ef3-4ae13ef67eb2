"""
Streamlit News Dashboard Application

A comprehensive dashboard for analyzing news articles with sentiment analysis,
indicator tracking, and semantic similarity search capabilities.
"""

import json
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import date, datetime, timedelta
import pytz
import logging
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
import traceback

from db.database import DatabaseManager, get_db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
EST_TIMEZONE = pytz.timezone("US/Eastern")
DEFAULT_DAYS_BACK = 7
RESULTS_PER_PAGE_OPTIONS = [10, 20, 50, 100]
SENTIMENT_OPTIONS = ["", "positive", "neutral", "negative"]
PLOTLY_TEMPLATE = "plotly_white"

# Custom CSS for enhanced styling
CUSTOM_CSS = """
<style>
    /* Main layout */
    .main {
        background-color: #f8fafc;
    }

    .sidebar .sidebar-content {
        background-color: #ffffff;
        border-right: 1px solid #e2e8f0;
    }

    /* Typography */
    h1, h2, h3 {
        color: #1a202c;
        font-weight: 600;
    }

    /* Metrics styling */
    .stMetric {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
    }

    /* Button styling */
    .stButton > button {
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        transition: all 0.2s;
    }

    .stButton > button:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Article containers */
    .article-container {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 16px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
    }

    /* Chunk content styling */
    .chunk-content {
        background: #f7fafc;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #3182ce;
        margin: 12px 0;
        font-size: 14px;
        line-height: 1.6;
    }

    /* Search result styling */
    .search-result {
        background: white;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 12px;
        border-left: 4px solid #38a169;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    /* Similarity score styling */
    .similarity-score {
        display: inline-block;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    /* Status indicators */
    .status-positive { color: #38a169; }
    .status-negative { color: #e53e3e; }
    .status-neutral { color: #718096; }

    /* Loading spinner */
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
    }
</style>
"""


class DashboardConfig:
    """Configuration class for dashboard settings."""

    def __init__(self):
        self.est_timezone = EST_TIMEZONE
        self.now_est = datetime.now(self.est_timezone)
        self.default_start = self.now_est - timedelta(days=DEFAULT_DAYS_BACK)
        self.default_end = self.now_est


class DatabaseService:
    """Service class for database operations with connection management."""

    def __init__(self):
        self._db = None

    @property
    def db(self) -> DatabaseManager:
        """Lazy initialization of database connection."""
        if self._db is None:
            try:
                self._db = get_db_manager()
                logger.info("Database connection established")
            except Exception as e:
                logger.error(f"Failed to initialize database: {e}")
                st.error(
                    "Failed to connect to database. Please check your configuration.")
                st.stop()
        return self._db

    def close(self):
        """Close database connection."""
        if self._db:
            try:
                self._db.close()
                logger.info("Database connection closed")
            except Exception as e:
                logger.error(f"Error closing database: {e}")

    @contextmanager
    def error_handler(self, operation: str):
        """Context manager for handling database operations."""
        try:
            yield
        except Exception as e:
            logger.error(f"Error in {operation}: {e}")
            st.error(
                f"An error occurred during {operation}. Please try again.")
            if st.checkbox("Show technical details", key=f"error_{operation}"):
                st.code(traceback.format_exc())


class ChartService:
    """Service class for creating charts and visualizations."""

    @staticmethod
    def create_sentiment_bar_chart(sentiment_data: Dict[str, float], title: str) -> go.Figure:
        """Create a sentiment analysis bar chart."""
        if not sentiment_data:
            return None

        df = pd.DataFrame([
            {'label': k, 'score': v}
            for k, v in sentiment_data.items()
        ])

        colors = {
            'positive': '#38a169',
            'negative': '#e53e3e',
            'neutral': '#718096'
        }

        fig = px.bar(
            df,
            x='label',
            y='score',
            title=title,
            color='label',
            color_discrete_map=colors,
            range_y=[0, 1],
            template=PLOTLY_TEMPLATE
        )

        fig.update_layout(
            height=300,
            showlegend=False,
            title_font_size=14
        )

        return fig

    @staticmethod
    def create_distribution_pie_chart(data: List[Dict], names_col: str, values_col: str, title: str) -> go.Figure:
        """Create a pie chart for data distribution."""
        if not data:
            return None

        df = pd.DataFrame(data)
        fig = px.pie(
            df,
            names=names_col,
            values=values_col,
            title=title,
            template=PLOTLY_TEMPLATE
        )

        fig.update_layout(height=400)
        return fig

    @staticmethod
    def create_time_series_chart(data: List[Dict], x_col: str, y_col: str, title: str) -> go.Figure:
        """Create a time series line chart."""
        if not data:
            return None

        df = pd.DataFrame(data)
        fig = px.line(
            df,
            x=x_col,
            y=y_col,
            title=title,
            template=PLOTLY_TEMPLATE
        )

        fig.update_layout(
            height=400,
            xaxis_title="Date",
            yaxis_title="Count"
        )

        return fig


class UIComponents:
    """Reusable UI components."""

    @staticmethod
    def render_article_card(article, show_chunks: bool = True):
        """Render an article card with optional chunks."""

        # Article header
        content = article['content'] or '[Article content not avaiable]'
        formatted_time = pd.to_datetime(article['date']).tz_convert(
            EST_TIMEZONE).strftime("%Y-%m-%d %H:%M EST") if article['date'] else 'N/A'
        st.markdown(f"""
        <div class="article-container">
            <h3>{article['title']}</h3>
            <p>{content[:1000]}{'...' if len(content) > 1000 else ''}</p>
            <div style="margin: 12px 0; color: #718096; font-size: 14px;">
                <strong>Source:</strong> {article['source']} | 
                <strong>Date:</strong> {formatted_time} |
                <strong>Tags:</strong> {', '.join(article['tags'])}
            </div>
        """, unsafe_allow_html=True)

        # Metadata
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            sentiment_meta = article['article_metadata'].get(
                'sentiment_analysis', {})
            sentiment = sentiment_meta.get('label', 'N/A')
            sentiment_scores = sentiment_meta.get('scores', {})
            sentiment_score = sentiment_scores['positive'] - \
                sentiment_scores['negative'] if sentiment_scores else 0.0
            sentiment_class = f"status-{sentiment}" if sentiment in [
                'positive', 'negative', 'neutral'] else ""
            st.markdown(f'<span class="{sentiment_class}"><strong>Sentiment:</strong> {sentiment} ({sentiment_score:.4f})</span>',
                        unsafe_allow_html=True)
        with col2:
            indicator_meta = article['article_metadata'].get(
                'indicator_analysis', {})
            indicator = indicator_meta.get('label', 'N/A')
            indicator_scores = indicator_meta.get('scores', {})
            indicator_score = indicator_scores['positive'] - \
                indicator_scores['negative'] if indicator_scores else 0.0
            st.markdown(
                f"<strong>Indicator:</strong> {indicator} ({indicator_score:.4f})", unsafe_allow_html=True)
        with col3:
            st.markdown(
                f"[🔗 Read Article]({article['url']})", unsafe_allow_html=True)

        influence_meta = article['article_metadata'].get('influence', {})
        if influence_meta:
            relevant = influence_meta.get('relevant')
            influence = influence_meta.get(
                'influence', 'N/A') if relevant == 'Yes' else 'Neutral'
            st.write(f"**Market influence:** {influence}")
            if relevant == 'Yes':
                st.write(f"**Reason:** {influence_meta.get('reason', 'N/A')}")

        # Chunks section
        if show_chunks and "chunks" in article:
            with st.expander(f"📄 Show Chunks ({len(article['chunks'])} total)", expanded=False):
                UIComponents.render_chunks(article['chunks'])

        st.markdown("</div>", unsafe_allow_html=True)

    @staticmethod
    def render_chunks(chunks):
        """Render article chunks with sentiment/indicator analysis."""
        for i, chunk in enumerate(chunks):
            st.markdown(
                f"**Chunk {chunk['chunk_index']}** ({chunk['model_name']})")

            # Sentiment analysis chart
            sentiment_meta = chunk['chunk_metadata'].get(
                'sentiment_analysis', {})
            sentiment = sentiment_meta.get('label', 'N/A')
            sentiment_scores = sentiment_meta.get('scores', {})
            # if sentiment_meta.get('scores'):
            #     fig = ChartService.create_sentiment_bar_chart(
            #         sentiment_meta['scores'],
            #         f"Sentiment Scores - Chunk {chunk['chunk_index']}"
            #     )
            #     if fig:
            #         st.plotly_chart(fig, use_container_width=True)

            # # Indicator analysis chart
            indicator_meta = chunk['chunk_metadata'].get(
                'indicator_analysis', {})
            indicator = indicator_meta.get('label', 'N/A')
            indicator_scores = indicator_meta.get('scores', {})
            # if indicator_meta.get('scores'):
            #     fig = ChartService.create_sentiment_bar_chart(
            #         indicator_meta['scores'],
            #         f"Indicator Scores - Chunk {chunk['chunk_index']}"
            #     )
            #     if fig:
            #         st.plotly_chart(fig, use_container_width=True)

            col1, col2 = st.columns(2)
            with col1:
                sentiment_score = sentiment_scores['positive'] - \
                    sentiment_scores['negative'] if sentiment_scores else 0.0
                sentiment_class = f"status-{sentiment}" if sentiment in [
                    'positive', 'negative', 'neutral'] else ""
                st.markdown(f'<span class="{sentiment_class}"><strong>Sentiment:</strong> {sentiment} ({sentiment_score:.4f})</span>',
                            unsafe_allow_html=True)
            with col2:
                indicator_score = indicator_scores['positive'] - \
                    indicator_scores['negative'] if indicator_scores else 0.0
                st.markdown(
                    f"<strong>Indicator:</strong> {indicator} ({indicator_score:.4f})", unsafe_allow_html=True)

            # Chunk text
            st.markdown(f'<div class="chunk-content">{chunk["text"]}</div>',
                        unsafe_allow_html=True)

            if i < len(chunks) - 1:
                st.markdown("---")

    @staticmethod
    def render_similarity_result(chunk, similarity: float):
        """Render a similarity search result."""
        sentiment_meta = chunk['chunk_metadata'].get('sentiment_analysis', {})
        indicator_meta = chunk['chunk_metadata'].get('indicator_analysis', {})

        st.markdown(f"""
        <div class="search-result">
            <div class="similarity-score">Similarity: {similarity:.4f}</div>
            <h4>{chunk.get('article_title', 'No title found')}</h4>
            <div style="margin: 8px 0; color: #718096; font-size: 14px;">
                <strong>Date:</strong> {chunk.get('article_date').strftime('%Y-%m-%d')} | 
                <a href="{chunk['article_url']}" target="_blank">🔗 Read Article</a>
            </div>
            <div style="margin: 8px 0; font-size: 14px;">
                <span class="status-{sentiment_meta.get('label', '')}">
                    <strong>Sentiment:</strong> {sentiment_meta.get('label', 'N/A')}
                </span>
                {f" | <strong>Indicator:</strong> {indicator_meta.get('label', 'N/A')}" if indicator_meta.get('label') else ""}
            </div>
            <div class="chunk-content">{chunk['text']}</div>
        </div>
        """, unsafe_allow_html=True)


class ArticlesPage:
    """Articles overview page."""

    def __init__(self, db_service: DatabaseService, config: DashboardConfig):
        self.db_service = db_service
        self.config = config

    def render_filters(self):
        """Render sidebar filters."""
        st.sidebar.header("🔍 Filters")

        with st.sidebar.expander("📅 Date Range", expanded=True):
            start_date = st.date_input(
                "Start Date", self.config.default_start.date())
            end_date = st.date_input(
                "End Date", self.config.default_end.date())

        source = st.sidebar.text_input(
            "📰 Source", help="Filter by news source")
        sentiment = st.sidebar.selectbox("😊 Sentiment", SENTIMENT_OPTIONS)
        indicator = st.sidebar.text_input(
            "📊 Indicator", help="Filter by indicator label")
        search_keyword = st.sidebar.text_input(
            "🔍 Search Keyword", help="Search in article titles")

        with st.sidebar.expander("⚙️ Display Options", expanded=True):
            limit = st.selectbox("Results per Page",
                                 RESULTS_PER_PAGE_OPTIONS, index=1)
            show_chunks = st.checkbox("Show Chunks", True)
            pagenum = st.number_input(
                "Page Number", min_value=1, step=1, value=1)

        return {
            'start_date': start_date,
            'end_date': end_date,
            'source': source or None,
            'sentiment': sentiment or None,
            'indicator': indicator or None,
            'search_keyword': search_keyword or None,
            'limit': limit,
            'offset': (pagenum - 1) * limit,
            'show_chunks': show_chunks
        }

    def render(self):
        """Render the articles page."""
        st.header("📄 Articles Overview")

        filters = self.render_filters()

        # Convert end date to end of day
        end_datetime = datetime.combine(
            filters['end_date'],
            datetime.max.time()
        ).astimezone(self.config.est_timezone)

        # Convert start date to start of day
        start_datetime = datetime.combine(
            filters['start_date'],
            datetime.min.time()
        ).astimezone(self.config.est_timezone)

        with self.db_service.error_handler("loading articles"):
            with st.spinner("Loading articles..."):
                articles = self.db_service.db.article_service.get_articles(
                    start_date=start_datetime,
                    end_date=end_datetime,
                    source=filters['source'],
                    sentiment=filters['sentiment'],
                    indicator=filters['indicator'],
                    search_keyword=filters['search_keyword'],
                    limit=filters['limit'],
                    offset=filters['offset'],
                    load_chunks=filters['show_chunks']
                )

        if articles:
            st.success(f"Found {len(articles)} articles")

            for article in articles:
                UIComponents.render_article_card(
                    article, filters['show_chunks'])
        else:
            st.info(
                "No articles found matching your criteria. Try adjusting the filters.")


class StatisticsPage:
    """Statistics dashboard page."""

    def __init__(self, db_service: DatabaseService, config: DashboardConfig):
        self.db_service = db_service
        self.config = config

    def render_filters(self):
        """Render sidebar filters."""
        st.sidebar.header("📊 Statistics Filters")

        with st.sidebar.expander("📅 Date Range", expanded=True):
            start_date = st.date_input("Start Date", None, key="stats_start")
            end_date = st.date_input("End Date", None, key="stats_end")
            source = st.text_input("📰 Source Filter", key="stats_source")

        return {
            'start_date': start_date,
            'end_date': end_date,
            'source': source or None
        }

    def render_summary_metrics(self, stats: Dict[str, Any]):
        """Render summary metrics."""
        st.subheader("📊 Summary Metrics")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("📰 Total Articles", f"{stats['total_articles']:,}")

        with col2:
            st.metric("📡 Unique Sources", stats['unique_sources'])

        with col3:
            # Get total chunks count
            try:
                result = self.db_service.db.article_service.run_sql_query(
                    "SELECT COUNT(*) AS total_chunks FROM chunks")
                chunk_count = result[0]["total_chunks"] if result else 0
                st.metric("📄 Total Chunks", f"{chunk_count:,}")
            except Exception:
                st.metric("📄 Total Chunks", "N/A")

        with col4:
            # Calculate average chunks per article
            if stats['total_articles'] > 0 and chunk_count > 0:
                avg_chunks = chunk_count / stats['total_articles']
                st.metric("📊 Avg Chunks/Article", f"{avg_chunks:.1f}")
            else:
                st.metric("📊 Avg Chunks/Article", "N/A")

    def render_distribution_charts(self, stats: Dict[str, Any]):
        """Render distribution charts."""
        col1, col2 = st.columns(2)

        with col1:
            if stats['by_source']:
                fig = px.bar(
                    pd.DataFrame(stats['by_source']),
                    x="source",
                    y="count",
                    title="📰 Articles by Source",
                    template=PLOTLY_TEMPLATE
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True,
                                key="source-bar")
            else:
                st.info("No source data available")

        with col2:
            if stats['by_sentiment']:
                fig = ChartService.create_distribution_pie_chart(
                    stats['by_sentiment'],
                    "sentiment",
                    "count",
                    "😊 Sentiment Distribution"
                )
                if fig:
                    st.plotly_chart(
                        fig, use_container_width=True, key="sentiment-pie")
            else:
                st.info("No sentiment data available")

        # Indicator distribution
        if stats['by_indicator']:
            fig = ChartService.create_distribution_pie_chart(
                stats['by_indicator'],
                "indicator",
                "count",
                "📊 Indicator Distribution"
            )
            if fig:
                st.plotly_chart(fig, use_container_width=True,
                                key="indicator-pie")

    def render_time_series(self, filters: Dict[str, Any]):
        """Render time series charts."""
        st.subheader("📈 Trends Over Time")

        start_date, end_date = filters['start_date'], filters['end_date']
        with self.db_service.error_handler("loading trend data"):
            # Article count trend
            trend_data = self.db_service.db.article_service.get_time_series_trend(
                start_date=start_date,
                end_date=end_date
            )

            if trend_data:
                fig = ChartService.create_time_series_chart(
                    trend_data,
                    "date",
                    "article_count",
                    "📈 Daily Article Count"
                )
                if fig:
                    st.plotly_chart(fig, use_container_width=True)

            # Sentiment trend (using raw SQL for complex aggregation)
            try:
                sentiment_query = f"""
                    SELECT
                        DATE(date),
                        AVG(CAST(article_metadata->'sentiment_analysis'->'scores'->>'positive' AS FLOAT)) AS avg_positive,
                        AVG(CAST(article_metadata->'sentiment_analysis'->'scores'->>'negative' AS FLOAT)) AS avg_negative,
                        AVG(CAST(article_metadata->'sentiment_analysis'->'scores'->>'neutral' AS FLOAT)) AS avg_neutral
                    FROM articles
                    WHERE 1=1
                    {"AND DATE(date) >= :start_date" if start_date else ''}
                    {"AND DATE(date) <= :end_date" if end_date else ''}
                    GROUP BY 1
                    ORDER BY 1
                """
                params = {}
                if start_date:
                    params["start_date"] = start_date
                if end_date:
                    params["end_date"] = end_date
                sentiment_trend = self.db_service.db.article_service.run_sql_query(
                    sentiment_query, params)

                if sentiment_trend:
                    df = pd.DataFrame(sentiment_trend)
                    fig = px.line(
                        df,
                        x="date",
                        y=["avg_positive", "avg_negative", "avg_neutral"],
                        title="📊 Average Daily Sentiment Scores",
                        template=PLOTLY_TEMPLATE
                    )
                    fig.update_layout(height=400)
                    st.plotly_chart(
                        fig, use_container_width=True, key="sentiment-line")
            except Exception as e:
                logger.error(f"Error rendering sentiment trend: {e}")
                st.info("Sentiment trend data not available")

    def render(self):
        """Render the statistics page."""
        st.header("📈 Statistics Dashboard")

        filters = self.render_filters()

        # Convert end date to end of day
        if filters['end_date']:
            filters['end_date'] = datetime.combine(
                filters['end_date'],
                datetime.max.time()
            ).astimezone(self.config.est_timezone)

        if filters['start_date']:
            filters['start_date'] = datetime.combine(
                filters['start_date'],
                datetime.min.time()
            ).astimezone(self.config.est_timezone)

        with self.db_service.error_handler("loading statistics"):
            with st.spinner("Loading statistics..."):
                stats = self.db_service.db.article_service.get_statistics(
                    start_date=filters['start_date'],
                    end_date=filters['end_date'],
                    source=filters['source']
                )

        if stats:
            self.render_summary_metrics(stats)
            self.render_distribution_charts(stats)
            self.render_time_series(filters)
        else:
            st.warning(
                "No statistical data available for the selected criteria.")


class SimilaritySearchPage:
    """Chunk similarity search page."""

    def __init__(self, db_service: DatabaseService, config: DashboardConfig):
        self.db_service = db_service
        self.config = config

    def render_search_form(self):
        """Render search form."""
        st.sidebar.header("🔍 Search Parameters")

        text = st.sidebar.text_area(
            "Search Text",
            height=100,
            help="Enter the text to search for semantically similar chunks."
        )

        with st.sidebar.expander("📅 Date Range", expanded=True):
            start_date = st.date_input(
                "Start Date", self.config.default_start.date())
            end_date = st.date_input(
                "End Date", self.config.default_end.date())

        with st.sidebar.expander("⚙️ Search Options", expanded=True):
            top_k = st.slider("Number of Results", 1, 50, 10)
            threshold = st.slider("Similarity Threshold", 0.0, 1.0, 0.0, 0.01)

        search_button = st.sidebar.button("🔍 Search", type="primary")

        return {
            'text': text,
            'start_date': start_date,
            'end_date': end_date,
            'top_k': top_k,
            'threshold': threshold if threshold > 0 else None,
            'search_clicked': search_button
        }

    def render(self):
        """Render the similarity search page."""
        st.header("🔗 Semantic Similarity Search")
        st.markdown(
            "Find chunks of text that are semantically similar to your search query.")

        search_params = self.render_search_form()

        if search_params['text'] and (search_params['search_clicked'] or len(search_params['text']) > 10):
            end_datetime = datetime.combine(
                search_params['end_date'],
                datetime.max.time()
            ).astimezone(self.config.est_timezone)
            start_datetime = datetime.combine(
                search_params['start_date'],
                datetime.min.time()
            ).astimezone(self.config.est_timezone)

            with self.db_service.error_handler("performing similarity search"):
                with st.spinner("Searching for similar chunks..."):
                    results = self.db_service.db.article_service.query_similar_chunks(
                        text=search_params['text'],
                        start_date=start_datetime,
                        end_date=end_datetime,
                        top_k=search_params['top_k'],
                        threshold=search_params['threshold']
                    )

            if results:
                st.success(f"Found {len(results)} similar chunks")

                # Show search summary
                with st.expander("🔍 Search Summary", expanded=True):
                    st.write(
                        f"**Query:** {search_params['text'][:200]}{'...' if len(search_params['text']) > 200 else ''}")
                    st.write(
                        f"**Date Range:** {search_params['start_date']} to {search_params['end_date']}")
                    st.write(
                        f"**Results:** {len(results)} of {search_params['top_k']} requested")
                    if search_params['threshold']:
                        st.write(
                            f"**Threshold:** {search_params['threshold']}")

                # Render results
                for chunk, similarity in results:
                    UIComponents.render_similarity_result(chunk, similarity)

            else:
                st.info("No similar chunks found. Try:")
                st.markdown("""
                - Using different keywords
                - Expanding the date range
                - Lowering the similarity threshold
                - Reducing the specificity of your search
                """)

        elif not search_params['text']:
            st.info(
                "👆 Enter your search text in the sidebar to begin searching for similar content.")
        else:
            st.info("Click the Search button or enter more text to start searching.")


class NewsDashboard:
    """Main dashboard application."""

    def __init__(self):
        self.config = DashboardConfig()
        self.db_service = DatabaseService()

        # Initialize pages
        self.articles_page = ArticlesPage(self.db_service, self.config)
        self.statistics_page = StatisticsPage(self.db_service, self.config)
        self.similarity_page = SimilaritySearchPage(
            self.db_service, self.config)

    def setup_page_config(self):
        """Configure Streamlit page settings."""
        st.set_page_config(
            page_title="News Analytics Dashboard",
            page_icon="📰",
            layout="wide",
            initial_sidebar_state="expanded"
        )

        # Apply custom CSS
        st.markdown(CUSTOM_CSS, unsafe_allow_html=True)

        # Main title
        st.title("📰 News Analytics Dashboard")
        st.markdown("---")

    def render_navigation(self):
        """Render navigation sidebar."""
        st.sidebar.title("🧭 Navigation")

        pages = {
            "📄 Articles Overview": "articles",
            "📊 Statistics": "statistics",
            "🔍 Similarity Search": "similarity"
        }

        selected_page = st.sidebar.radio(
            "Select Page",
            list(pages.keys()),
            key="navigation"
        )

        return pages[selected_page]

    def run(self):
        """Run the dashboard application."""
        try:
            self.setup_page_config()

            # Render navigation and get selected page
            current_page = self.render_navigation()

            # Render the selected page
            if current_page == "articles":
                self.articles_page.render()
            elif current_page == "statistics":
                self.statistics_page.render()
            elif current_page == "similarity":
                self.similarity_page.render()

        except Exception as e:
            logger.error(f"Application error: {e}")
            st.error("An unexpected error occurred. Please refresh the page.")
            if st.checkbox("Show error details"):
                st.code(traceback.format_exc())

        finally:
            # Cleanup is handled by Streamlit's session management
            pass


# Application entry point
if __name__ == "__main__":
    # Create and run the dashboard
    dashboard = NewsDashboard()
    dashboard.run()
