#!/usr/bin/env python3
"""
Command-line interface for LLM-based market prediction.

This script provides a CLI for making market predictions using LLMs.
"""

from utils.logging_config import get_predictor_logger
from predictor.llm.llm_predictor import LLMPredictor
import asyncio
import argparse
import json
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__)))))


# Configure logger
logger = get_predictor_logger(__name__)

TZ_NAME = "America/New_York"


async def make_llm_prediction(
    api_name: str = "gemini",
    model_name: Optional[str] = None,
    max_articles: int = 15,
    output_file: Optional[str] = None
) -> Dict[str, Any]:
    """Make an LLM-based market prediction."""

    # Initialize LLM predictor
    logger.info(f"Initializing LLM predictor with API: {api_name}")
    predictor = LLMPredictor(use_cache=False)

    # Use shared data preparation
    from predictor.llm.data_preparation import prepare_prediction_data

    logger.info("Preparing prediction data using direct database access")
    data = prepare_prediction_data(
        use_web_services=False, max_articles=max_articles * 2)

    articles = data['articles']
    current_price = data['current_price']
    price_data = data['price_data']
    vix_level = data['vix_level']

    if not articles:
        logger.warning("No articles available")

    if price_data is None or price_data.empty:
        logger.error("No price data available")
        return {}

    # Make prediction
    logger.info(f"Making prediction with {len(articles)} articles")
    result = await predictor.predict_market_direction(
        articles=articles,
        current_price=current_price,
        price_data=price_data,
        vix_level=vix_level,
        preferred_api=api_name,
        model_name=model_name,
        max_articles=max_articles
    )

    # Add metadata
    result['cli_metadata'] = {
        'articles_used': len(articles),
        'current_price': current_price,
        'prediction_time': datetime.now().isoformat(),
        'api_requested': api_name
    }

    # Save to file if requested
    if output_file:
        try:
            with open(output_file, 'w') as f:
                json.dump(result, f, indent=2)
            logger.info(f"Prediction saved to {output_file}")
        except Exception as e:
            logger.error(f"Error saving to file: {e}")

    return result


def print_prediction_summary(result: Dict[str, Any]):
    """Print a formatted summary of the prediction."""
    print("\n" + "="*60)
    print("LLM MARKET PREDICTION SUMMARY")
    print("="*60)

    # Basic prediction
    prediction = result.get('prediction', 'unknown').upper()
    confidence = result.get('confidence', 0.0)

    print(f"Prediction: {prediction}")
    print(f"Confidence: {confidence:.1%}")

    # Probabilities
    probabilities = result.get('probabilities', {})
    print(f"\nProbabilities:")
    for direction, prob in probabilities.items():
        print(f"  {direction.capitalize()}: {prob:.1%}")

    # Key evidence
    evidence = result.get('key_evidence', [])
    if evidence:
        print(f"\nKey Evidence:")
        for i, item in enumerate(evidence[:3], 1):
            print(f"  {i}. {item}")

    # Dominant theme
    theme = result.get('dominant_theme', '')
    if theme:
        print(f"\nDominant Theme: {theme}")

    # Critical levels
    levels = result.get('critical_levels', {})
    if levels:
        print(f"\nCritical Levels:")
        if 'support' in levels:
            print(f"  Support: {levels['support']}")
        if 'resistance' in levels:
            print(f"  Resistance: {levels['resistance']}")
        if 'trigger' in levels:
            print(f"  Trigger: {levels['trigger']}")

    # Metadata
    metadata = result.get('metadata', {})
    api_used = metadata.get('api', 'unknown')
    model_used = metadata.get('model', 'unknown')

    print(f"\nAPI Used: {api_used}")
    print(f"Model: {model_used}")

    # CLI metadata
    cli_meta = result.get('cli_metadata', {})
    articles_used = cli_meta.get('articles_used', 0)
    current_price = cli_meta.get('current_price', 0)

    print(f"Articles Analyzed: {articles_used}")
    print(f"Current SP500 Price: ${current_price:.2f}")

    print("="*60)


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="LLM-based SP500 market prediction",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python predict_cli.py --api openai --max-articles 10
  python predict_cli.py --api anthropic --output prediction.json
        """
    )

    parser.add_argument(
        '--api',
        choices=['openai', 'anthropic', 'gemini'],
        default='gemini',
        help='LLM API to use for prediction (default: gemini)'
    )

    parser.add_argument(
        '--model',
        type=str,
        help='LLM model to use for prediction (default: gemini-2.0-flash-001)'
    )

    parser.add_argument(
        '--max-articles',
        type=int,
        default=15,
        help='Maximum number of articles to analyze (default: 15)'
    )

    parser.add_argument(
        '--output',
        type=str,
        help='Output file to save prediction results (JSON format)'
    )
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Suppress detailed output, only show summary'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.quiet:
        logger.setLevel('WARNING')

    async def run_prediction():
        try:
            result = await make_llm_prediction(
                api_name=args.api,
                model_name=args.model,
                max_articles=args.max_articles,
                output_file=args.output
            )

            print_prediction_summary(result)

            return 0

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            print(f"\nError: {e}")
            return 1

    # Run the async function
    exit_code = asyncio.run(run_prediction())
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
