"""
LLM-based SP500 market predictor.

This module provides LLM-based prediction capabilities using news sentiment
and technical indicators to predict SP500 market direction.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import pandas as pd

from apis.llm.base import BaseAPIManager
from apis.llm.openai import OpenAIManager
from apis.llm.anthropic import AnthropicManager
from apis.llm.gemini import GeminiManager
from apis.llm.data_types import CompletionRequest, CompletionStatus
from db.database import get_db_manager
from predictor.llm.data_formatter import LLMDataFormatter
from predictor.llm.response_parser import LLMResponseParser
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class LLMPredictor:
    """LLM-based market predictor using news sentiment and technical indicators."""

    def __init__(
        self,
        api_configs: Optional[Dict[str, Dict[str, Any]]] = None,
        use_cache: bool = True,
        cache_duration_hours: int = 1
    ):
        """
        Initialize the LLM predictor.

        Args:
            api_configs: Configuration for different LLM APIs
            use_cache: Whether to use database caching for predictions
            cache_duration_hours: How long to cache predictions
        """
        self.use_cache = use_cache
        self.cache_duration_hours = cache_duration_hours

        # Initialize components
        self.data_formatter = LLMDataFormatter()
        self.response_parser = LLMResponseParser()
        self.db = get_db_manager() if use_cache else None

        # Initialize API managers
        self.api_managers = {}
        if api_configs:
            self._initialize_api_managers(api_configs)
        else:
            self._initialize_default_api_managers()

    def _initialize_api_managers(self, api_configs: Dict[str, Dict[str, Any]]):
        """Initialize API managers with custom configurations."""
        api_classes = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager,
            'gemini': GeminiManager
        }

        for api_name, config in api_configs.items():
            if api_name in api_classes:
                try:
                    self.api_managers[api_name] = api_classes[api_name](
                        **config)
                    logger.info(f"Initialized {api_name} API manager")
                except Exception as e:
                    logger.error(
                        f"Failed to initialize {api_name} API manager: {e}")

    def _initialize_default_api_managers(self):
        """Initialize API managers with default configurations."""
        try:
            self.api_managers['gemini'] = GeminiManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized Gemini API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini API manager: {e}")

        try:
            self.api_managers['openai'] = OpenAIManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized OpenAI API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI API manager: {e}")

        try:
            self.api_managers['anthropic'] = AnthropicManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized Anthropic API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic API manager: {e}")

    async def predict_market_direction(
        self,
        articles: List[Dict[str, Any]],
        current_price: float,
        price_data: Optional[pd.DataFrame] = None,
        vix_level: Optional[float] = None,
        preferred_api: str = "gemini",
        model_name: Optional[str] = None,
        max_articles: int = 15
    ) -> Dict[str, Any]:
        """
        Predict market direction using LLM analysis.

        Args:
            articles: List of news articles with sentiment scores
            current_price: Current SP500 price
            price_data: Historical price data for technical indicators
            vix_level: Current VIX level
            preferred_api: Preferred API to use for prediction
            model_name: Model name to use for prediction
            max_articles: Maximum number of articles to analyze

        Returns:
            Structured prediction result
        """
        try:
            # Check cache first
            if self.use_cache:
                cached_result = await self._get_cached_prediction(articles, current_price)
                if cached_result:
                    logger.info("Returning cached LLM prediction")
                    return cached_result
            # Format technical indicators
            technical_indicators = self.data_formatter.format_technical_indicators(
                current_price=current_price,
                price_data=price_data,
                vix_level=vix_level
            )

            # Create prompt
            user_prompt, system_prompt = self.data_formatter.create_market_prediction_prompt(
                articles=articles,
                technical_indicators=technical_indicators,
                max_articles=max_articles
            )

            # Get prediction from LLM
            prediction_result = await self._get_llm_prediction(
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                preferred_api=preferred_api,
                model_name=model_name
            )

            # Add article metadata to prediction result for reference mapping
            article_metadata = self.data_formatter.get_article_metadata()
            prediction_result = self._enrich_prediction_with_article_refs(
                prediction_result, article_metadata
            )

            # Add input metadata
            prediction_result['input_metadata'] = {
                'num_articles': len(articles),
                'current_price': current_price,
                'vix_level': vix_level,
                'technical_indicators': technical_indicators,
                'article_metadata': article_metadata
            }

            # Cache the result
            if self.use_cache:
                await self._cache_prediction(prediction_result, articles, current_price)

            return prediction_result

        except Exception as e:
            logger.error(f"Error in LLM market prediction: {e}")
            raise

    async def _get_llm_prediction(
        self,
        user_prompt: str,
        system_prompt: str,
        preferred_api: str = "gemini",
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get prediction from LLM API."""

        if preferred_api in self.api_managers:
            try:
                return await self._call_api(
                    self.api_managers[preferred_api],
                    user_prompt,
                    system_prompt,
                    preferred_api,
                    model_name
                )
            except Exception as e:
                logger.error(
                    f"Failed to get prediction from {preferred_api}: {e}")
                raise
        else:
            # If preferred API is not available, try fallback APIs
            available_apis = list(self.api_managers.keys())
            if available_apis:
                fallback_api = available_apis[0]
                logger.warning(
                    f"Preferred API {preferred_api} not available, using {fallback_api}")
                return await self._call_api(
                    self.api_managers[fallback_api],
                    user_prompt,
                    system_prompt,
                    fallback_api
                )
            else:
                raise Exception("No LLM APIs are available")

    async def _call_api(
        self,
        api_manager: BaseAPIManager,
        user_prompt: str,
        system_prompt: str,
        api_name: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Call specific LLM API for prediction."""

        # Get the appropriate model name for the API
        model_name = model_name or self._get_model_name_for_api(api_name)

        # Create completion request
        request = CompletionRequest(
            user_prompt=user_prompt,
            system_prompt=system_prompt,
            model=model_name,
            temperature=0.1  # Low temperature for consistent predictions
        )

        # Make API call
        response = api_manager.get_completion(request)

        if response.status != CompletionStatus.SUCCEEDED.value:
            raise Exception(f"API call failed with status: {response.status}")

        # Parse response
        prediction_result = self.response_parser.parse_market_prediction(
            response_content=response.content,
            api_name=api_name,
            model_name=model_name
        )

        return prediction_result

    def _enrich_prediction_with_article_refs(
        self,
        prediction_result: Dict[str, Any],
        article_metadata: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """Enrich prediction result with article URLs and metadata."""

        def enrich_evidence_list(evidence_list):
            """Enrich a list of evidence items with article metadata."""
            if not evidence_list or evidence_list is None:
                return []

            enriched_evidence = []
            for evidence in evidence_list:
                if evidence is None:
                    continue
                if isinstance(evidence, dict) and evidence.get('article_ref'):
                    article_ref = evidence['article_ref']
                    if article_ref and article_ref in article_metadata:
                        metadata = article_metadata[article_ref]
                        evidence['url'] = metadata.get('url', '')
                        evidence['publish_time'] = metadata.get(
                            'publish_time', '')
                        # Ensure title and source are from metadata if not already set
                        if not evidence.get('article_title'):
                            evidence['article_title'] = metadata.get(
                                'title', '')
                        if not evidence.get('source'):
                            evidence['source'] = metadata.get('source', '')
                enriched_evidence.append(evidence)
            return enriched_evidence

        # Enrich key evidence
        if 'key_evidence' in prediction_result:
            prediction_result['key_evidence'] = enrich_evidence_list(
                prediction_result['key_evidence']
            )

        # Enrich detailed outlook sections
        if 'detailed_outlook' in prediction_result:
            for timeframe in ['short_term', 'medium_term', 'long_term']:
                if timeframe in prediction_result['detailed_outlook']:
                    outlook = prediction_result['detailed_outlook'][timeframe]
                    if 'key_evidence' in outlook:
                        outlook['key_evidence'] = enrich_evidence_list(
                            outlook['key_evidence']
                        )

        # Enrich dominant theme supporting articles
        if 'dominant_theme' in prediction_result and isinstance(prediction_result['dominant_theme'], dict):
            theme = prediction_result['dominant_theme']
            if 'supporting_articles' in theme and theme['supporting_articles'] is not None:
                enriched_articles = []
                for article in theme['supporting_articles']:
                    if article is None:
                        continue
                    if isinstance(article, dict) and article.get('article_ref'):
                        article_ref = article['article_ref']
                        if article_ref and article_ref in article_metadata:
                            metadata = article_metadata[article_ref]
                            article['url'] = metadata.get('url', '')
                            article['publish_time'] = metadata.get(
                                'publish_time', '')
                            # Ensure title and source are from metadata if not already set
                            if not article.get('article_title'):
                                article['article_title'] = metadata.get(
                                    'title', '')
                            if not article.get('source'):
                                article['source'] = metadata.get('source', '')
                    enriched_articles.append(article)
                theme['supporting_articles'] = enriched_articles

        return prediction_result

    def _get_model_name_for_api(self, api_name: str) -> str:
        """Get the appropriate model name for the given API."""
        model_map = {
            'openai': 'o4-mini',
            'anthropic': 'claude-opus-4-20250514',
            'gemini': 'gemini-2.5-pro-preview-06-05'
            # 'gemini': 'gemini-2.0-flash-001'
        }
        return model_map.get(api_name)

    async def _get_cached_prediction(
        self,
        articles: List[Dict[str, Any]],
        current_price: float
    ) -> Optional[Dict[str, Any]]:
        """Get cached prediction if available and recent."""
        if not self.db:
            return None

        try:
            # Create cache key based on articles and price
            cache_key = self._create_cache_key(articles, current_price)

            # Check for recent cached prediction
            cutoff_time = datetime.now() - timedelta(hours=self.cache_duration_hours)

            # Query database for cached predictions
            # This would need to be implemented in the database service
            # For now, return None to skip caching
            return None

        except Exception as e:
            logger.error(f"Error checking cache: {e}")
            return None

    async def _cache_prediction(
        self,
        prediction_result: Dict[str, Any],
        articles: List[Dict[str, Any]],
        current_price: float
    ):
        """Cache prediction result."""
        if not self.db:
            return

        try:
            # Create cache entry
            cache_key = self._create_cache_key(articles, current_price)

            # Store in database
            # This would need to be implemented in the database service
            logger.debug(f"Cached prediction with key: {cache_key}")

        except Exception as e:
            logger.error(f"Error caching prediction: {e}")

    def _create_cache_key(self, articles: List[Dict[str, Any]], current_price: float) -> str:
        """Create cache key for prediction."""
        # Simple cache key based on article count, latest article time, and price
        if not articles:
            return f"llm_pred_{current_price}_{datetime.now().strftime('%Y%m%d%H')}"

        latest_time = max(
            article.get('publish_time', '') for article in articles
        )

        return f"llm_pred_{len(articles)}_{latest_time}_{current_price:.2f}"

    def get_supported_apis(self) -> List[str]:
        """Get list of supported/initialized APIs."""
        return list(self.api_managers.keys())

    async def test_api_connection(self, api_name: str) -> bool:
        """Test connection to specific API."""
        if api_name not in self.api_managers:
            return False

        try:
            # Simple test request
            model_name = self._get_model_name_for_api(api_name)
            test_request = CompletionRequest(
                user_prompt="Test",
                system_prompt="Respond with 'OK'",
                model=model_name,
                max_tokens=10,
                temperature=0.0
            )

            response = self.api_managers[api_name]._make_completion_request(
                test_request)
            return response.status == CompletionStatus.SUCCEEDED.value

        except Exception as e:
            logger.error(f"API test failed for {api_name}: {e}")
            return False
