#!/usr/bin/env python3
"""
Shared data preparation utilities for LLM predictions.

This module provides unified data preparation functions that can be used
by both CLI and web server components to ensure consistency.
"""

import pandas as pd
import pytz
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from utils.logging_config import get_predictor_logger

logger = get_predictor_logger(__name__)

TZ_NAME = "America/New_York"


def get_recent_articles_from_db(limit: int = 20) -> List[Dict[str, Any]]:
    """
    Get recent articles from the database in the format expected by LLM predictor.

    Args:
        limit: Maximum number of articles to return

    Returns:
        List of formatted articles
    """
    try:
        from db.database import get_db_manager

        db = get_db_manager()
        articles = db.article_service.get_articles_by_influence(
            limit=limit,
            start_date=datetime.now(
                pytz.timezone(TZ_NAME)) - timedelta(days=3),
            influence_field="influence_tagging.influence"
        )

        # Convert to the format expected by LLM predictor
        formatted_articles = []
        for article in articles:
            formatted_article = {
                'title': article.get('title', ''),
                'content': article.get('content', ''),
                'source': article.get('source', ''),
                'publish_time': article.get('date', ''),
                'url': article.get('url', '')
            }
            formatted_articles.append(formatted_article)

        logger.info(
            f"Retrieved {len(formatted_articles)} articles from database")
        return formatted_articles

    except Exception as e:
        logger.error(f"Error retrieving articles from database: {e}")
        return []


def get_recent_articles_from_web_service(limit: int = 20) -> List[Dict[str, Any]]:
    """
    Get recent articles using the web service data layer.

    Args:
        limit: Maximum number of articles to return

    Returns:
        List of formatted articles
    """
    try:
        # Try to import and use web service, but handle database connection issues
        try:
            from web.data.news_data import get_all_news

            # Get articles without filters to get the most recent ones
            news_data = get_all_news(limit=limit)
            articles = news_data.get('all', [])

            # Convert to the format expected by LLM predictor
            formatted_articles = []
            for article in articles:
                formatted_article = {
                    'title': article.get('title', ''),
                    'content': article.get('content', ''),
                    'source': article.get('source', ''),
                    'publish_time': article.get('publish_time', ''),
                    'url': article.get('url', '')
                }
                formatted_articles.append(formatted_article)

            logger.info(
                f"Retrieved {len(formatted_articles)} articles from web service")
            return formatted_articles
        except ImportError as ie:
            logger.warning(f"Could not import web service modules: {ie}")
            return []

    except Exception as e:
        logger.error(f"Error retrieving articles from web service: {e}")
        # If web service fails, try direct database access as fallback
        logger.info("Falling back to direct database access for articles")
        return get_recent_articles_from_db(limit)


def get_index_price_data() -> Optional[pd.DataFrame]:
    """
    Get current index price data using Yahoo Finance API.

    Returns:
        DataFrame with price data or None if failed
    """
    try:
        from apis.yahoo_finance import YahooFinanceAPI

        yahoo_api = YahooFinanceAPI()
        df = yahoo_api.get_price_data(
            ticker="SPY",
            interval="1d",
            period="30d"
        )
        return df
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return None


def get_index_price_data_web() -> Optional[pd.DataFrame]:
    """
    Get current index price data using web service data layer.

    Returns:
        DataFrame with price data or None if failed
    """
    try:
        # Try to import and use web service, but handle database connection issues
        try:
            from web.data.sp500_data import get_sp500_data

            # Get SP500 data from web service
            sp500_data = get_sp500_data()

            if not sp500_data or 'error' in sp500_data:
                logger.warning("No SP500 data available from web service")
                return None

            # Convert to DataFrame format expected by LLM predictor
            if sp500_data.get('dates') and sp500_data.get('close'):
                df = pd.DataFrame({
                    'Open': sp500_data.get('open', []),
                    'High': sp500_data.get('high', []),
                    'Low': sp500_data.get('low', []),
                    'Close': sp500_data.get('close', []),
                    'Volume': sp500_data.get('volume', [])
                })

                # Set dates as index
                dates = pd.to_datetime(sp500_data['dates'])
                df.index = dates

                return df
            else:
                logger.warning("Invalid SP500 data format from web service")
                return None
        except ImportError as ie:
            logger.warning(f"Could not import web service modules: {ie}")
            return None

    except Exception as e:
        logger.error(f"Error fetching price data from web service: {e}")
        # If web service fails, try direct API access as fallback
        logger.info("Falling back to direct API access for price data")
        return get_index_price_data()


def get_vix_level() -> Optional[float]:
    """
    Get current VIX level.

    Returns:
        Current VIX level or None if failed
    """
    try:
        import yfinance as yf
        vix = yf.Ticker("^VIX")
        vix_data = vix.history(period="5d")
        return float(vix_data['Close'].iloc[-1])
    except Exception as e:
        logger.error(f"Error fetching VIX data: {e}")
        return None


def prepare_prediction_data(
    use_web_services: bool = False,
    max_articles: int = 20
) -> Dict[str, Any]:
    """
    Prepare all data needed for LLM prediction.

    Args:
        use_web_services: Whether to use web service data layers instead of direct DB access
        max_articles: Maximum number of articles to retrieve

    Returns:
        Dictionary containing all prepared data
    """
    logger.info(f"Preparing prediction data (web_services={use_web_services})")

    # Get articles
    if use_web_services:
        articles = get_recent_articles_from_web_service(limit=max_articles)
        price_data = get_index_price_data_web()
    else:
        articles = get_recent_articles_from_db(limit=max_articles)
        price_data = get_index_price_data()

    # Get current price
    current_price = 500.0  # Default fallback
    if price_data is not None and not price_data.empty:
        current_price = float(price_data['Close'].iloc[-1])

    # Get VIX level
    vix_level = get_vix_level()

    return {
        'articles': articles,
        'current_price': current_price,
        'price_data': price_data,
        'vix_level': vix_level or 0.0,
        'num_articles': len(articles)
    }
