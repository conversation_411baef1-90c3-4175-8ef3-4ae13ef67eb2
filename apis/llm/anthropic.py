from anthropic import Anthropic
import json
import os
from typing import Any, Dict, List, Optional
from apis.llm.base import BaseAPIManager

# Import logging configuration
from apis.llm.data_types import BatchResponse, BatchStatus, CompletionRequest, CompletionResponse, CompletionStatus
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='anthropic.log')


class AnthropicManager(BaseAPIManager):
    """Anthropic API implementation."""

    def __init__(self, total_budget: float = 0.0, total_cost: float = 0.0, requests_per_minute: int = 30, **kwargs):
        model_pricing = {
            'claude-3-5-haiku': {
                'input_rate': 0.0008,
                'output_rate': 0.004
            },
            'claude-sonnet-4': {
                'input_rate': 0.003,
                'output_rate': 0.015
            },
            'claude-opus-4': {
                'input_rate': 0.015,
                'output_rate': 0.075
            }
        }

        super().__init__(total_budget=total_budget,
                         total_cost=total_cost,
                         requests_per_minute=requests_per_minute,
                         model_pricing=model_pricing,
                         batch_discount=0.5,
                         **kwargs)

        self.client = self._initialize_client()

    def _initialize_client(self):
        return Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))

    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        messages = [{"role": "user", "content": request.user_prompt}]
        message = self.client.messages.create(
            model=request.model,
            system=request.system_prompt,
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        return CompletionResponse(
            content=message.content[0].text,
            status=CompletionStatus.SUCCEEDED.value,
            model=message.model,
            input_tokens=message.usage.input_tokens,
            output_tokens=message.usage.output_tokens,
            raw_response=message.to_json()
        )

    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        from anthropic.types.messages.batch_create_params import Request
        from anthropic.types.message_create_params import MessageCreateParamsNonStreaming

        batch_requests = []
        for request in requests:
            batch_requests.append(
                Request(
                    custom_id=request.custom_id,
                    params=MessageCreateParamsNonStreaming(
                        model=request.model,
                        max_tokens=request.max_tokens,
                        temperature=request.temperature,
                        system=request.system_prompt,
                        messages=[
                            {"role": "user", "content": request.user_prompt}]
                    )
                )
            )

        response = self.client.messages.batches.create(requests=batch_requests)

        return BatchResponse(
            id=response.id,
            status=response.processing_status,
            created_at=response.created_at,
            expires_at=response.expires_at,
            raw_response=response.to_json()
        )

    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        response = self.client.messages.batches.retrieve(batch_id)
        status = response.processing_status

        return BatchResponse(
            id=response.id,
            status=status,
            created_at=response.created_at,
            expires_at=response.expires_at,
            completed_at=response.ended_at,
            raw_response=response.to_json()
        )

    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        results = []
        for result in self.client.messages.batches.results(batch_id):
            message = result.result.message
            status = CompletionStatus.SUCCEEDED.value if result.result.type == "succeeded"\
                else CompletionStatus.FAILED.value
            results.append(CompletionResponse(
                content=message.content[0].text,
                status=status,
                model=message.model,
                input_tokens=message.usage.input_tokens,
                output_tokens=message.usage.output_tokens,
                raw_response=result.to_json(),
                custom_id=result.custom_id
            ))

        return results

    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all Message Batches."""
        response = self.client.messages.batches.list(limit=limit)
        batches = []
        for response_line in response.data:
            batches.append(BatchResponse(
                id=response_line.id,
                status=response_line.processing_status,
                created_at=response_line.created_at,
                expires_at=response_line.expires_at,
                completed_at=response_line.ended_at,
                raw_response=response_line.to_json()
            ))
        return batches
