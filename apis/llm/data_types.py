from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class BatchStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"
    CANCELLING = "cancelling"
    CANCELLED = "cancelled"
    VALIDATING = "validating"
    FINALIZING = "finalizing"
    ENDED = "ended"
    PROCESSED = "processed"


class CompletionStatus(Enum):
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    IN_PROGRESS = "in_progress"


ACTIVE_BATCH_STATUSES = [
    BatchStatus.IN_PROGRESS.value,
    BatchStatus.VALIDATING.value,
    BatchStatus.FINALIZING.value
]


COMPLETED_BATCH_STATUSES = [
    BatchStatus.COMPLETED.value,
    BatchStatus.ENDED.value
]

FAILED_BATCH_STATUSES = [
    BatchStatus.FAILED.value,
    BatchStatus.EXPIRED.value,
    BatchStatus.CANCELLED.value,
    BatchStatus.CANCELLING.value
]


@dataclass
class CompletionRequest:
    """Represents a completion request with metadata."""
    user_prompt: str
    system_prompt: str
    model: str
    temperature: float
    max_tokens: Optional[int] = None
    estimated_tokens: Optional[int] = None
    estimated_cost: Optional[float] = None
    custom_id: Optional[str] = None  # required for batch requests

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class CompletionResponse:
    """Represents a completion response with metadata."""
    content: str
    status: CompletionStatus
    model: str
    input_tokens: int
    output_tokens: int
    raw_response: str
    cost: Optional[float] = None
    custom_id: Optional[str] = None  # required for batch requests

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class BatchResponse:
    """Represents a batch job with metadata."""
    id: str
    status: str
    created_at: datetime
    raw_response: str
    expires_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_tokens: Optional[int] = None
    estimated_cost: Optional[float] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    cost: Optional[float] = None
    completion_results: Optional[List[CompletionResponse]] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
