# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy
from datetime import datetime


class NewsItem(scrapy.Item):
    """
    Item for storing financial news articles
    """
    # Original fields
    id = scrapy.Field()
    title = scrapy.Field()
    url = scrapy.Field()
    source = scrapy.Field()
    publish_time = scrapy.Field()
    update_time = scrapy.Field()
    date = scrapy.Field()
    content = scrapy.Field()
    author = scrapy.Field()
    tags = scrapy.Field()
    crawl_time = scrapy.Field(default=datetime.now)

    # Sentiment analysis field
    sentiment_analysis = scrapy.Field()  # Contains sentiment label and scores
    indicator_analysis = scrapy.Field()  # Contains indicator label and scores
    llm_analysis = scrapy.Field()  # Contains LLM analysis result
    chunks = scrapy.Field()
