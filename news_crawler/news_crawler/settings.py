# Scrapy settings for news_crawler project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html

BOT_NAME = "news_crawler"

SPIDER_MODULES = ["news_crawler.spiders"]
NEWSPIDER_MODULE = "news_crawler.spiders"


# Crawl responsibly by identifying yourself (and your website) on the user-agent
# USER_AGENT is defined below with a more descriptive value

# Configure maximum concurrent requests performed by Scrapy (default: 16)
CONCURRENT_REQUESTS = 16

# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
DOWNLOAD_DELAY = 3  # seconds between requests
# The download delay setting will honor only one of:
CONCURRENT_REQUESTS_PER_DOMAIN = 4  # Reduced for more polite crawling
CONCURRENT_REQUESTS_PER_IP = 4  # Limit requests per IP

# Respect robots.txt directives
ROBOTSTXT_OBEY = True

# Add a custom user agent that identifies your crawler
USER_AGENT = "NewsMonitor Financial Crawler (+https://example.com/bot.html)"

# Add a delay for downloading large files
DOWNLOAD_TIMEOUT = 60  # 60 seconds timeout for downloads

# Depth settings for nested URL crawling
# DEPTH_LIMIT = 3  # Maximum depth to crawl
# Prioritize breadth-first crawling (1) over depth-first (0)
# DEPTH_PRIORITY = 1
# DEPTH_STATS_VERBOSE = True  # Show verbose depth statistics

# Duplicate filter settings
DUPEFILTER_CLASS = 'scrapy.dupefilters.RFPDupeFilter'
DUPEFILTER_DEBUG = True  # Show debug info for duplicate filter

# Disable cookies (enabled by default)
# COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
# TELNETCONSOLE_ENABLED = False

# Override the default request headers:
# DEFAULT_REQUEST_HEADERS = {
#    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
#    "Accept-Language": "en",
# }

# Enable or disable spider middlewares
# See https://docs.scrapy.org/en/latest/topics/spider-middleware.html
# SPIDER_MIDDLEWARES = {
#    "news_crawler.middlewares.NewsCrawlerSpiderMiddleware": 543,
# }

# Enable or disable downloader middlewares
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
DOWNLOADER_MIDDLEWARES = {
    # Enable built-in retry middleware
    'scrapy.downloadermiddlewares.retry.RetryMiddleware': 90,
    # Enable redirect middleware
    'scrapy.downloadermiddlewares.redirect.RedirectMiddleware': 100,
    # Enable cookie middleware
    'scrapy.downloadermiddlewares.cookies.CookiesMiddleware': 130,
    # Enable HTTP compression middleware
    'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': 810,
    # Enable HTTP cache middleware
    'scrapy.downloadermiddlewares.httpcache.HttpCacheMiddleware': 900,
    # Enable URLDedupMiddleware
    'news_crawler.middlewares.URLDedupMiddleware': 1000,
}

# Enable or disable extensions
# See https://docs.scrapy.org/en/latest/topics/extensions.html
# EXTENSIONS = {
#     "scrapy.extensions.corestats.CoreStats": 500,
#     "scrapy.extensions.telnet.TelnetConsole": 500,
# }

# Configure item pipelines
# See https://docs.scrapy.org/en/latest/topics/item-pipeline.html
ITEM_PIPELINES = {
    "news_crawler.pipelines.PreFilteringPipeline": 150,
    "news_crawler.pipelines.PreprocessingPipeline": 160,
    "news_crawler.pipelines.SentimentAnalysisPipeline": 200,
    "news_crawler.pipelines.LlmAnalysisPipeline": 280,
    "news_crawler.pipelines.DBWriterPipeline": 300,
}

# Enable and configure the AutoThrottle extension for polite crawling
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
AUTOTHROTTLE_ENABLED = True
# The initial download delay
AUTOTHROTTLE_START_DELAY = 5
# The maximum download delay to be set in case of high latencies
AUTOTHROTTLE_MAX_DELAY = 60
# The average number of requests Scrapy should be sending in parallel to
# each remote server
AUTOTHROTTLE_TARGET_CONCURRENCY = 2.0
# Enable showing throttling stats for every response received:
AUTOTHROTTLE_DEBUG = True

# Enable and configure HTTP caching
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
# These settings are defined below

# Set settings whose default value is deprecated to a future-proof value
TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
FEED_EXPORT_ENCODING = "utf-8"

# Logging settings
# Set to INFO to disable DEBUG logs (options: DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL = "INFO"
# LOG_FILE = "scrapy.log"


# Retry settings
RETRY_ENABLED = True
RETRY_TIMES = 3  # Number of retries per request
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]  # HTTP codes to retry on

# Enable and configure HTTP caching to avoid crawling the same pages multiple times
HTTPCACHE_ENABLED = True
# 7 days - longer cache time for financial news
HTTPCACHE_EXPIRATION_SECS = 86400 * 7
HTTPCACHE_DIR = ".scrapy/httpcache"  # Store cache in the .scrapy directory
HTTPCACHE_IGNORE_HTTP_CODES = [503, 504, 505, 429]  # Don't cache server errors
HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"
# Use RFC2616 policy for better caching
HTTPCACHE_POLICY = "scrapy.extensions.httpcache.RFC2616Policy"
HTTPCACHE_GZIP = True  # Compress the cached responses
HTTPCACHE_ALWAYS_STORE = True  # Always store responses in cache
HTTPCACHE_DBM_MODULE = "dbm.gnu"  # Use GNU DBM for better performance if available

# Additional cache settings
HTTPCACHE_IGNORE_MISSING = False  # Don't ignore missing cache entries
HTTPCACHE_IGNORE_SCHEMES = ['file']  # Don't cache file:// scheme URLs
HTTPCACHE_IGNORE_RESPONSE_CACHE_CONTROLS = [
    'no-store', 'no-cache', 'must-revalidate']  # Ignore these cache controls

# We're using Scrapy's built-in HTTP caching instead of a custom URL cache

# Date format settings
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD

# CLOSESPIDER_ITEMCOUNT = 200
MAX_BACKTRACK_DAYS = 365 * 3
ANALYSIS_TASKS = ["sentiment", "indicator"]
MAX_WORDS_ARTICLE_CONTENT = 1500
MIN_WORDS_ARTICLE_CONTENT = 100

# LLM analysis settings
LLM_API_NAME = 'gemini'
LLM_PROMPT_TYPE = 'influence_tagging'
LLM_MODEL = 'gemini-2.0-flash-001'
LLM_TOTAL_BUDGET = 100.0
