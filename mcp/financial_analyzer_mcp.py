import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
import asyncpg
import openai
from anthropic import Anthropic
import os
from dataclasses import dataclass
from apis.llm.anthropic import AnthropicManager
from apis.llm.openai import OpenAIManager
from db.database import get_db_manager
from mcp import types
from mcp.server import Server
from mcp.server.stdio import stdio_server

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='financial_analyzer_mcp.log')


@dataclass
class LlmConfig:
    """Configuration for llm settings."""
    api_name: str = 'openai'
    model: str = "gpt-4.1-nano"
    budget_limit: float = 1.0
    max_tokens: int = 512
    requests_per_minute: int = 2


class FinancialAnalyzer:
    def __init__(self):
        self.db = get_db_manager().article_service
        self.llm_config = LlmConfig()
        self.llm_manager = self._initialize_llm_api()

    def _initialize_llm_api(self):
        """Initialize the appropriate LLM API based on configuration."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        api_class = api_mapping.get(self.llm_config.api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {self.llm_config.api_name}")

        return api_class(
            model=self.llm_config.model,
            budget_limit=self.llm_config.budget_limit,
            requests_per_minute=self.llm_config.requests_per_minute
        )

    async def analyze_news_impact_for_date(self, target_date: str) -> Dict[str, Any]:
        """Analyze news impact for a specific date"""
        try:
            # Parse target date
            target_dt = datetime.strptime(target_date, "%Y-%m-%d")

            # Get articles from past 7 days
            start_date = target_dt - timedelta(days=7)
            end_date = target_dt + timedelta(days=1)

            articles = await self.db.get_articles_by_date_range(start_date, end_date)

            # Get SPY price for target date
            spy_prices = await self.db.get_spy_prices_by_date_range(target_dt, target_dt + timedelta(days=1))
            spy_price = spy_prices[0]['price'] if spy_prices else None

            if not spy_price:
                # Get latest available price
                latest_price = await self.db.get_latest_spy_price()
                spy_price = latest_price['price'] if latest_price else 400.0

            # Analyze news impact
            analysis = await self.llm_manager.analyze_news_impact(articles, spy_price, target_date)

            return {
                "target_date": target_date,
                "spy_price": spy_price,
                "articles_analyzed": len(articles),
                "analysis": analysis,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"News impact analysis failed: {e}")
            return {"status": "error", "error": str(e)}

    async def predict_spy_trend_analysis(self, days_back: int = 30) -> Dict[str, Any]:
        """Predict SPY trend based on recent data"""
        try:
            # Get historical price data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            historical_prices = await self.db_manager.get_spy_prices_by_date_range(start_date, end_date)

            # Get recent news for context
            news_start = end_date - timedelta(days=7)
            recent_articles = await self.db_manager.get_articles_by_date_range(news_start, end_date)

            # Get news analysis first
            latest_price = historical_prices[-1]['price'] if historical_prices else 400.0
            news_analysis = await self.llm_manager.analyze_news_impact(
                recent_articles, latest_price, end_date.strftime("%Y-%m-%d")
            )

            # Generate trend prediction
            trend_prediction = await self.llm_manager.predict_spy_trend(historical_prices, news_analysis)

            return {
                "analysis_date": end_date.strftime("%Y-%m-%d"),
                "historical_data_points": len(historical_prices),
                "recent_articles_count": len(recent_articles),
                "current_spy_price": latest_price,
                "news_analysis": news_analysis,
                "trend_prediction": trend_prediction,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Trend prediction failed: {e}")
            return {"status": "error", "error": str(e)}


# MCP Server Implementation
app = Server("financial-analyzer")
analyzer = FinancialAnalyzer()


@app.list_tools()
async def list_tools() -> List[types.Tool]:
    """List available tools"""
    return [
        types.Tool(
            name="analyze_news_impact",
            description="Analyze news articles from past 7 days that may have influenced SPY price on a specific date",
            inputSchema={
                "type": "object",
                "properties": {
                    "target_date": {
                        "type": "string",
                        "description": "Target date to analyze (YYYY-MM-DD format)"
                    }
                },
                "required": ["target_date"]
            }
        ),
        types.Tool(
            name="predict_spy_trend",
            description="Predict future SPY price trend based on historical data and recent news",
            inputSchema={
                "type": "object",
                "properties": {
                    "days_back": {
                        "type": "integer",
                        "description": "Number of days of historical data to analyze (default: 30)",
                        "default": 30
                    }
                }
            }
        ),
        types.Tool(
            name="get_market_summary",
            description="Get current market summary with latest SPY price and recent news count",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        )
    ]


@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Handle tool calls"""

    if name == "analyze_news_impact":
        target_date = arguments.get("target_date")
        if not target_date:
            return [types.TextContent(type="text", text="Error: target_date is required")]

        result = await analyzer.analyze_news_impact_for_date(target_date)
        return [types.TextContent(type="text", text=json.dumps(result, indent=2, default=str))]

    elif name == "predict_spy_trend":
        days_back = arguments.get("days_back", 30)
        result = await analyzer.predict_spy_trend_analysis(days_back)
        return [types.TextContent(type="text", text=json.dumps(result, indent=2, default=str))]

    elif name == "get_market_summary":
        try:
            latest_price = await analyzer.db_manager.get_latest_spy_price()
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            recent_articles = await analyzer.db_manager.get_articles_by_date_range(start_date, end_date)

            summary = {
                "current_spy_price": latest_price['price'] if latest_price else "N/A",
                "last_updated": latest_price['datetime'] if latest_price else "N/A",
                "recent_articles_count": len(recent_articles),
                "status": "success"
            }
            return [types.TextContent(type="text", text=json.dumps(summary, indent=2, default=str))]
        except Exception as e:
            return [types.TextContent(type="text", text=f"Error getting market summary: {str(e)}")]

    else:
        return [types.TextContent(type="text", text=f"Unknown tool: {name}")]


async def main():
    """Main entry point"""
    try:
        async with stdio_server() as streams:
            await app.run(streams[0], streams[1], app.create_initialization_options())
    except KeyboardInterrupt:
        logger.info("Shutting down...")

if __name__ == "__main__":
    asyncio.run(main())
