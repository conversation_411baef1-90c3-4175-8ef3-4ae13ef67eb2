"""
Centralized path utilities for the NewsMonitor project.

This module provides consistent path resolution functions that can be used
across all components to locate project directories and files.
"""

import os
from pathlib import Path
from typing import Optional


def get_project_root() -> Path:
    """
    Get the root directory of the NewsMonitor project.

    This function searches upward from the current file location to find
    the project root, identified by the presence of key project directories.

    Returns:
        Path: Path to the project root directory

    Raises:
        RuntimeError: If the project root cannot be found
    """
    # Start from the current file and go up
    current_path = Path(__file__).resolve()

    # Look for project root indicators
    project_indicators = [
        'web',
        'news_crawler',
        'predictor',
        'db',
        'nlp',
        'apis',
        'utils'
    ]

    # Search upward for the project root
    while current_path != current_path.parent:
        # Check if this directory contains the expected project structure
        if all((current_path / indicator).exists() for indicator in project_indicators[:3]):
            return current_path
        current_path = current_path.parent

    # Fallback: if we can't find the full structure, look for any indicator
    current_path = Path(__file__).resolve()
    while current_path != current_path.parent:
        if any((current_path / indicator).exists() for indicator in project_indicators):
            return current_path
        current_path = current_path.parent

    raise RuntimeError("Could not find NewsMonitor project root directory")


def get_data_directory() -> Path:
    """
    Get the main data directory for the project.

    Returns:
        Path: Path to the data directory
    """
    data_dir = get_project_root() / 'data'
    data_dir.mkdir(exist_ok=True)
    return data_dir


def get_logs_directory(component: Optional[str] = None) -> Path:
    """
    Get the appropriate logs directory for a component.

    Args:
        component: Component name ('web', 'crawler', 'predictor', etc.)
                  If None, uses the main logs directory

    Returns:
        Path: Path to the logs directory
    """
    project_root = get_project_root()

    if component == 'web':
        logs_dir = project_root / 'web' / 'logs'
    elif component == 'crawler':
        logs_dir = project_root / 'news_crawler' / 'logs'
    elif component == 'predictor':
        logs_dir = project_root / 'predictor' / 'logs'
    elif component == 'nlp':
        logs_dir = project_root / 'nlp' / 'logs'
    elif component == 'apis':
        logs_dir = project_root / 'apis' / 'logs'
    elif component == 'db':
        logs_dir = project_root / 'db' / 'logs'
    else:
        # Default to main logs directory
        logs_dir = project_root / 'logs'

    # Ensure directory exists
    logs_dir.mkdir(parents=True, exist_ok=True)
    return logs_dir


def get_output_directory(component: Optional[str] = None) -> Path:
    """
    Get the appropriate output directory for a component.

    Args:
        component: Component name ('web', 'crawler', 'predictor', etc.)
                  If None, uses the main output directory

    Returns:
        Path: Path to the output directory
    """
    project_root = get_project_root()

    if component == 'crawler':
        output_dir = project_root / 'news_crawler' / 'output'
    elif component == 'predictor':
        output_dir = project_root / 'predictor' / 'output'
    elif component == 'nlp':
        output_dir = project_root / 'nlp' / 'output'
    else:
        # Default to main output directory
        output_dir = project_root / 'output'

    # Ensure directory exists
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def get_config_directory() -> Path:
    """
    Get the configuration directory for the project.

    Returns:
        Path: Path to the config directory
    """
    config_dir = get_project_root() / 'config'
    config_dir.mkdir(exist_ok=True)
    return config_dir


def get_component_directory(component: str) -> Path:
    """
    Get the directory for a specific component.

    Args:
        component: Component name ('web', 'crawler', 'predictor', 'db', 'nlp', 'apis', 'utils')

    Returns:
        Path: Path to the component directory

    Raises:
        ValueError: If the component is not recognized
    """
    project_root = get_project_root()

    component_map = {
        'web': 'web',
        'crawler': 'news_crawler',
        'predictor': 'predictor',
        'db': 'db',
        'nlp': 'nlp',
        'apis': 'apis',
        'utils': 'utils'
    }

    if component not in component_map:
        raise ValueError(
            f"Unknown component: {component}. Valid components: {list(component_map.keys())}")

    component_dir = project_root / component_map[component]

    if not component_dir.exists():
        raise FileNotFoundError(
            f"Component directory not found: {component_dir}")

    return component_dir


def resolve_path(path: str, relative_to: Optional[str] = None) -> Path:
    """
    Resolve a path relative to the project root or a specific component.

    Args:
        path: Path to resolve (can be absolute or relative)
        relative_to: Component name to resolve relative to, or None for project root

    Returns:
        Path: Resolved absolute path
    """
    path_obj = Path(path)

    # If already absolute, return as-is
    if path_obj.is_absolute():
        return path_obj

    # Resolve relative to specified component or project root
    if relative_to:
        base_dir = get_component_directory(relative_to)
    else:
        base_dir = get_project_root()

    return base_dir / path_obj


def ensure_directory(path: Path) -> Path:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        path: Path to the directory

    Returns:
        Path: The directory path (for chaining)
    """
    path.mkdir(parents=True, exist_ok=True)
    return path
