"""
Text processing utilities for the NewsMonitor project.
Provides functions for sentence splitting, stop word removal, and other text processing tasks.
Supports multiple backends (NLTK, spaCy) with graceful fallbacks.
"""

import re
import string
import logging
import difflib
from typing import List, Optional, Tuple, Dict, Any

# Import configuration
from nlp.config import (
    SIMILARITY_THRESHOLD,
    MIN_SENTENCE_LENGTH
)

# Define text processing backends
BACKEND_NLTK = 'nltk'
BACKEND_SPACY = 'spacy'
BACKEND_REGEX = 'regex'  # Fallback

# Configure logger for this module
logger = logging.getLogger(__name__)

# Track available backends
available_backends = {
    BACKEND_REGEX: True  # Regex is always available as fallback
}

# Basic stop words for fallback
BASIC_STOP_WORDS = {
    'a', 'an', 'the', 'and', 'or', 'but', 'if', 'because', 'as', 'what',
    'while', 'of', 'to', 'in', 'for', 'on', 'by', 'with', 'about', 'against',
    'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'from', 'up', 'down', 'is', 'am', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'i', 'me', 'my',
    'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours', 'yourself',
    'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself',
    'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'this',
    'that', 'these', 'those', 'would', 'should', 'could', 'ought', 'i\'m', 'you\'re',
    'he\'s', 'she\'s', 'it\'s', 'we\'re', 'they\'re', 'i\'ve', 'you\'ve', 'we\'ve',
    'they\'ve', 'i\'d', 'you\'d', 'he\'d', 'she\'d', 'we\'d', 'they\'d', 'i\'ll',
    'you\'ll', 'he\'ll', 'she\'ll', 'we\'ll', 'they\'ll', 'isn\'t', 'aren\'t',
    'wasn\'t', 'weren\'t', 'hasn\'t', 'haven\'t', 'hadn\'t', 'doesn\'t', 'don\'t',
    'didn\'t', 'won\'t', 'wouldn\'t', 'shan\'t', 'shouldn\'t', 'can\'t', 'cannot',
    'couldn\'t', 'mustn\'t', 'let\'s', 'that\'s', 'who\'s', 'what\'s', 'here\'s',
    'there\'s', 'when\'s', 'where\'s', 'why\'s', 'how\'s', 'which', 'who', 'whom',
    'whose', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few',
    'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own',
    'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don',
    'now', 'd', 'll', 'm', 'o', 're', 've', 'y', 'ain', 'aren', 'couldn', 'didn',
    'doesn', 'hadn', 'hasn', 'haven', 'isn', 'ma', 'mightn', 'mustn', 'needn',
    'shan', 'shouldn', 'wasn', 'weren', 'won', 'wouldn'
}

# Try to import NLTK
try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import sent_tokenize

    # Download necessary NLTK data if not already downloaded
    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords')

    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')

    NLTK_STOP_WORDS = set(stopwords.words('english'))
    available_backends[BACKEND_NLTK] = True
    logger.info("NLTK backend available for text processing")
except ImportError:
    NLTK_STOP_WORDS = set()
    available_backends[BACKEND_NLTK] = False
    logger.warning(
        "NLTK not available. Some text processing features will be limited.")

# Try to import spaCy
try:
    import spacy
    # Load a small English model for efficiency
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        # If the model isn't installed, try to download it
        logger.info("Downloading spaCy model 'en_core_web_sm'...")
        spacy.cli.download("en_core_web_sm")
        nlp = spacy.load("en_core_web_sm")

    SPACY_STOP_WORDS = nlp.Defaults.stop_words
    available_backends[BACKEND_SPACY] = True
    logger.info("spaCy backend available for text processing")
except ImportError:
    SPACY_STOP_WORDS = set()
    available_backends[BACKEND_SPACY] = False
    logger.warning(
        "spaCy not available. Some text processing features will be limited.")


def get_available_backends() -> List[str]:
    """
    Get a list of available text processing backends.

    Returns:
        List of available backend names.
    """
    return [backend for backend, available in available_backends.items() if available]


def get_best_backend():
    """
    Get the best available text processing backend.

    Returns:
        str: The name of the best available backend
    """
    available_backends = get_available_backends()
    logger.debug(f"Available NLP backends: {', '.join(available_backends)}")

    if BACKEND_SPACY in available_backends:
        backend = BACKEND_SPACY
        logger.debug("Using spaCy backend for text processing")
    elif BACKEND_NLTK in available_backends:
        backend = BACKEND_NLTK
        logger.debug("Using NLTK backend for text processing")
    else:
        backend = BACKEND_REGEX
        logger.debug("Using regex backend for text processing")

    return backend


def split_into_sentences(text: str) -> List[str]:
    """
    Split text into sentences using the specified backend.

    Args:
        text: Text to split.

    Returns:
        List of sentences.
    """
    # Handle empty text
    if not text:
        return []

    backend = get_best_backend()

    sentences = []
    # Try spaCy first
    if backend == BACKEND_SPACY:
        try:
            doc = nlp(text)
            sentences = [sent.text for sent in doc.sents]
        except Exception as e:
            logger.debug(
                f"Error using spaCy sentence tokenizer: {e}. Trying NLTK.")
    elif backend == BACKEND_NLTK:
        try:
            sentences = sent_tokenize(text)
        except Exception as e:
            logger.debug(
                f"Error using NLTK sentence tokenizer: {e}. Falling back to regex.")
    else:
        # Fallback: Simple sentence splitting by punctuation
        sentences = re.split(r'(?<=[.!?])\s+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

    return sentences


def remove_stop_words(sentence: str, backend: str = BACKEND_REGEX) -> str:
    """
    Remove stop words from a sentence using the specified backend.

    Args:
        sentence: The sentence to process.
        backend: Text processing backend to use (spacy, nltk, or regex).
            Falls back to available backends if the specified one is not available.
            Default is spaCy, then NLTK, then basic stop words.

    Returns:
        The sentence with stop words removed.
    """
    # Handle empty or very short sentences
    if not sentence or len(sentence) < 3:
        return sentence

    # Split the sentence into words
    words = sentence.split()

    if backend == BACKEND_SPACY and available_backends.get(BACKEND_SPACY):
        # Use spaCy's stop words
        filtered_words = [word for word in words if word.lower(
        ) not in SPACY_STOP_WORDS and len(word) > 2]

    # Use NLTK if available and either specified or spaCy not available
    elif backend == BACKEND_NLTK and available_backends.get(BACKEND_NLTK):
        # Use NLTK's stop words
        filtered_words = [word for word in words if word.lower(
        ) not in NLTK_STOP_WORDS and len(word) > 2]

    # Fallback to basic stop words
    else:
        # Use our basic stop words set
        filtered_words = [word for word in words if word.lower(
        ) not in BASIC_STOP_WORDS and len(word) > 2]

    # Join the filtered words back into a sentence
    return ' '.join(filtered_words)


def remove_punctuation(sentence: str) -> str:
    """
    Remove punctuation from a sentence.

    Args:
        sentence: The sentence to process.

    Returns:
        The sentence with punctuation removed.
    """
    # Handle empty sentences
    if not sentence:
        return sentence

    # Create a translation table to remove all punctuation
    # This is more efficient than using regex for simple punctuation removal
    translator = str.maketrans('', '', string.punctuation)

    # Apply the translation and normalize whitespace
    cleaned = sentence.translate(translator)

    # Normalize whitespace (replace multiple spaces with a single space)
    cleaned = ' '.join(cleaned.split())

    return cleaned


def calculate_similarity(sentence1: str, sentence2: str) -> float:
    """
    Calculate similarity between two sentences.

    Args:
        sentence1: First sentence.
        sentence2: Second sentence.

    Returns:
        Similarity score between 0.0 and 1.0.
    """
    # Handle empty sentences
    if not sentence1 or not sentence2:
        return 0.0

    # Convert to lowercase for case-insensitive comparison
    s1_lower = sentence1.lower()
    s2_lower = sentence2.lower()

    # Use difflib's SequenceMatcher to calculate similarity
    # This is more accurate but slower than simple metrics
    return difflib.SequenceMatcher(None, s1_lower, s2_lower).ratio()


def filter_sentences(sentences: List[str], similarity_threshold: float = SIMILARITY_THRESHOLD) -> List[str]:
    """
    Filter sentences to remove duplicates and near-duplicates.

    Args:
        sentences: List of sentences to filter.
        similarity_threshold: Threshold for considering sentences as similar.

    Returns:
        Filtered list of sentences.
    """
    if not sentences:
        return []

    # If we have very few sentences, don't filter further
    if len(sentences) <= 3:
        return sentences

    # Use a set to track exact duplicates more efficiently
    seen_sentences = set()
    unique_sentences = []

    for sentence in sentences:
        # Skip exact duplicates using set lookup (much faster)
        if sentence in seen_sentences:
            continue

        # Check for near-duplicates using similarity calculation
        is_similar = False
        for unique_sentence in unique_sentences:
            if calculate_similarity(sentence, unique_sentence) > similarity_threshold:
                is_similar = True
                break

        # If not similar to any existing sentence, add it
        if not is_similar:
            unique_sentences.append(sentence)
            seen_sentences.add(sentence)

    return unique_sentences


def process_text(text: str, backend: str = BACKEND_REGEX) -> List[str]:
    """
    Process text through the full pipeline: sentence splitting, punctuation removal,
    stop word removal, and duplicate filtering.

    Args:
        text: Text to process.
        backend: Text processing backend to use (spacy, nltk, or regex).
            Default is spaCy, then NLTK, then regex.

    Returns:
        List of processed sentences.
    """
    # Split into sentences
    sentences = split_into_sentences(text, backend)

    # Process each sentence
    processed_sentences = []
    for sentence in sentences:
        # Remove punctuation
        no_punct_sentence = remove_punctuation(sentence)

        # Remove stop words
        cleaned_sentence = remove_stop_words(no_punct_sentence, backend)

        # Only add if not empty after processing and meets minimum length
        if cleaned_sentence and len(cleaned_sentence) >= MIN_SENTENCE_LENGTH:
            processed_sentences.append(cleaned_sentence)

    # Filter sentences to remove duplicates and near-duplicates
    filtered_sentences = filter_sentences(processed_sentences)

    return filtered_sentences
