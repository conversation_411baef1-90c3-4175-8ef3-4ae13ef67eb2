"""
Flask-SQLAlchemy database configuration for the NewsMonitor web application.

This module provides Flask-SQLAlchemy integration for proper session management
and resolves detached instance issues with Flask-Login.
"""

import os
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.orm import DeclarativeBase
from dotenv import load_dotenv

from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)

# Load environment variables
load_dotenv(override=True)


class Base(DeclarativeBase):
    """Base class for SQLAlchemy models."""
    pass


# Initialize Flask-SQLAlchemy with custom base class
db = SQLAlchemy(model_class=Base)


def init_db(app):
    """Initialize database with Flask app."""
    # Configure database URL
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is required")

    app.config['SQLALCHEMY_DATABASE_URI'] = database_url
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': 10,
        'max_overflow': 20,
        'pool_pre_ping': True,
        'echo': False  # Set to True for SQL debugging
    }

    # Initialize the database with the app
    db.init_app(app)

    return db
