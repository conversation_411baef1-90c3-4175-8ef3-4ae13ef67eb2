"""
Database migration script to update User model schema.

This script handles the migration from the old User model to the new Flask-SQLAlchemy
compatible User model, specifically renaming the is_active column to active to avoid
conflicts with Flask-Login's UserMixin.is_active property.
"""

import os
import sys
from sqlalchemy import text
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web.database import init_db, db
from flask import Flask

# Load environment variables
load_dotenv()

def create_migration_app():
    """Create a minimal Flask app for database operations."""
    app = Flask(__name__)
    
    # Configure database
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is required")
    
    app.config['SQLALCHEMY_DATABASE_URI'] = database_url
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    init_db(app)
    
    return app

def migrate_user_schema():
    """Migrate User schema to resolve Flask-Login conflicts."""
    app = create_migration_app()
    
    with app.app_context():
        try:
            # Check if migration is needed
            result = db.session.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'is_active'
            """)).fetchone()
            
            if result:
                print("Found 'is_active' column, performing migration...")
                
                # Rename is_active to active
                db.session.execute(text("""
                    ALTER TABLE users RENAME COLUMN is_active TO active
                """))
                
                # Update indexes
                db.session.execute(text("""
                    DROP INDEX IF EXISTS ix_users_email_active
                """))
                db.session.execute(text("""
                    DROP INDEX IF EXISTS ix_users_username_active
                """))
                
                db.session.execute(text("""
                    CREATE INDEX ix_users_email_active ON users (email, active)
                """))
                db.session.execute(text("""
                    CREATE INDEX ix_users_username_active ON users (username, active)
                """))
                
                db.session.commit()
                print("Migration completed successfully!")
                
            else:
                print("Migration not needed - 'active' column already exists")
                
        except Exception as e:
            db.session.rollback()
            print(f"Migration failed: {e}")
            raise

if __name__ == "__main__":
    migrate_user_schema()
