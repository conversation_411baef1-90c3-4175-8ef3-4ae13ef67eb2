#!/usr/bin/env python3
"""
Test script for the NewsMonitor email system.

This script tests the email functionality including:
- Email service initialization
- Daily summary generation
- Email template rendering
- Database operations
"""

import traceback
from flask_mail import Message
from utils.logging_config import get_web_logger
from flask import Flask, render_template
import os
import sys
import asyncio
from datetime import datetime, timedelta
from web.auth.routes import auth_bp
from web.email_service.service import EmailService
from web.models import EmailLog, User
from web.database import init_db, db

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


logger = get_web_logger(__name__)


def create_test_app():
    """Create a test Flask app for email testing."""
    app = Flask(__name__, template_folder='templates')

    # Configure the app
    app.config['SECRET_KEY'] = 'test-secret-key'
    # app.config['TESTING'] = True
    app.config['MAIL_SERVER'] = 'localhost'
    app.config['MAIL_PORT'] = 1025  # MailHog default port
    app.config['MAIL_USE_TLS'] = False
    app.config['MAIL_USE_SSL'] = False
    app.config['MAIL_USERNAME'] = None
    app.config['MAIL_PASSWORD'] = None
    app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
    app.config['BASE_URL'] = 'http://localhost:5000'
    app.config['SERVER_NAME'] = 'localhost:5000'
    app.config['PREFERRED_URL_SCHEME'] = 'http'
    app.config['MAIL_DEBUG'] = True
    app.config['APPLICATION_ROOT'] = '/web'

    import logging
    app.logger.setLevel(logging.DEBUG)

    init_db(app)

    @app.route('/')
    def index():
        return render_template('index.html')

    app.register_blueprint(auth_bp)
    return app


def test_email_service_initialization():
    """Test email service initialization."""
    print("Testing email service initialization...")

    app = create_test_app()

    with app.app_context():
        try:
            email_service = EmailService(app)
            print("✅ Email service initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Email service initialization failed: {e}")
            return False


def test_database_connection():
    """Test database connection and user operations."""
    print("Testing database connection...")
    app = create_test_app()

    with app.app_context():
        try:
            db.create_all()
            print("✅ Database connection successful")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    try:
        user_count = db.session.query(User).count()
        print(
            f"✅ Database connection successful. Found {user_count} users.")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def create_test_user():
    """Create a test user for email testing."""
    print("Creating test user...")

    try:
        # Check if test user already exists
        existing_user = db.session.query(User).filter_by(
            email='<EMAIL>').first()
        if existing_user:
            print("✅ Test user already exists")
            return existing_user

        # Create new test user
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            is_active=True,
            is_verified=True,
            email_preferences={
                'daily_summary': True,
                'market_alerts': True,
                'news_digest': True,
                'frequency': 'daily'
            }
        )
        test_user.set_password('testpassword123')

        db.session.add(test_user)
        db.session.commit()
        db.session.refresh(test_user)

        print("✅ Test user created successfully")
        return test_user

    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return None


def test_daily_summary_generation():
    """Test daily summary data generation."""
    print("Testing daily summary generation...")

    app = create_test_app()

    with app.app_context():
        try:
            email_service = EmailService(app)
            summary_data = email_service._generate_daily_summary_data()

            print("✅ Daily summary data generated successfully")
            print(f"   - Date: {summary_data.get('date')}")
            print(
                f"   - Market data available: {'market_data' in summary_data}")
            print(f"   - News data available: {'news' in summary_data}")
            print(f"   - Prediction available: {'prediction' in summary_data}")

            return True

        except Exception as e:
            print(f"❌ Daily summary generation failed: {e}")
            return False


def test_email_template_rendering():
    """Test email template rendering."""
    print("Testing email template rendering...")

    app = create_test_app()

    with app.app_context():
        try:
            # Create test user
            test_user = create_test_user()
            if not test_user:
                return False

            email_service = EmailService(app)

            # Generate test data
            test_data = {
                'date': datetime.now(),
                'market_data': {
                    'current_price': 4500.00,
                    'price_change': 25.50,
                    'price_change_percent': 0.57,
                    'volume': 1234567,
                    'high': 4525.00,
                    'low': 4475.00
                },
                'news': {
                    'all': [
                        {
                            'title': 'Test Market News Article',
                            'source': 'Test Source',
                            'date': datetime.now(),
                            'content': 'This is a test news article for email template testing.',
                            'url': 'https://example.com/test-article'
                        }
                    ]
                },
                'prediction': {
                    'prediction_label': 'Bullish',
                    'confidence': 0.75
                }
            }

            # Test HTML template rendering
            from flask import render_template
            html_content = render_template(
                'email/daily_summary.html', user=test_user, data=test_data)

            if html_content and len(html_content) > 100:
                print("✅ HTML email template rendered successfully")
                print(f"   - Template length: {len(html_content)} characters")
            else:
                print("❌ HTML template rendering failed or too short")
                return False

            # Test text template rendering
            text_content = render_template(
                'email/daily_summary.txt', user=test_user, data=test_data)

            if text_content and len(text_content) > 50:
                print("✅ Text email template rendered successfully")
                print(f"   - Template length: {len(text_content)} characters")
            else:
                print("❌ Text template rendering failed or too short")
                return False

            return True

        except Exception as e:
            print(f"❌ Email template rendering failed: {e}")
            print(traceback.format_exc())
            return False


def test_email_sending():
    """Test email sending (requires mail server)."""
    print("Testing email sending...")

    app = create_test_app()

    with app.app_context():
        try:
            # Create test user
            test_user = create_test_user()
            if not test_user:
                return False

            email_service = EmailService(app)

            # Try to send a test daily summary
            success = email_service.send_daily_summary(test_user)

            if success:
                print("✅ Email sent successfully")

                # Check email log
                recent_log = db.session.query(EmailLog).filter_by(
                    user_id=test_user.id,
                    email_type='daily_summary'
                ).order_by(EmailLog.sent_at.desc()).first()

                if recent_log:
                    print(
                        f"   - Email logged with status: {recent_log.status}")

                return True
            else:
                print("❌ Email sending failed")
                return False

        except Exception as e:
            print(f"❌ Email sending test failed: {e}")
            print("   Note: This might be expected if no mail server is configured")
            return False


def run_all_tests():
    """Run all email system tests."""
    print("🧪 Starting NewsMonitor Email System Tests\n")

    tests = [
        ("Database Connection", test_database_connection),
        ("Email Service Initialization", test_email_service_initialization),
        # ("Daily Summary Generation", test_daily_summary_generation),
        # ("Email Template Rendering", test_email_template_rendering),
        ("Email Sending", test_email_sending),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Email system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
