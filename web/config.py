"""
Configuration settings for the web interface.

This module provides configuration settings for the web interface,
including paths, URLs, and other settings.
"""

import os
from pathlib import Path

# Project paths
ROOT_DIR = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT_DIR / "templates"
STATIC_DIR = ROOT_DIR / "static"
LOG_DIR = ROOT_DIR / "logs"

# Ensure directories exist
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# Web server settings
HOST = "0.0.0.0"
PORT = 5000
DEBUG = False

# Data settings
DEFAULT_LOOKBACK_DAYS = 30  # Default number of days to look back for data
SP500_TICKER = "SPY"  # S&P 500 index ticker symbol
DEFAULT_ARTICLE_LIMIT = 20  # Default number of articles to return

# Date format
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD
DISPLAY_DATE_FORMAT = "%b %d, %Y"  # e.g., Jan 01, 2023

# Email settings
MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true'
MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'false').lower() == 'true'
MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
MAIL_DEFAULT_SENDER = os.environ.get(
    'MAIL_DEFAULT_SENDER', '<EMAIL>')

# Celery settings
CELERY_BROKER_URL = os.environ.get(
    'CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get(
    'CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# Application settings
BASE_URL = os.environ.get('BASE_URL', 'http://localhost:5000')
ENABLE_EMAIL_SCHEDULER = os.environ.get(
    'ENABLE_EMAIL_SCHEDULER', 'false').lower() == 'true'
