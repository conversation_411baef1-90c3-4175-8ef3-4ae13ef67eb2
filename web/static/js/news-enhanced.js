/**
 * Enhanced News JavaScript Module
 * Yahoo Finance-style news layout with featured news and advanced search
 */

class EnhancedNews {
    constructor() {
        this.allArticles = [];
        this.featuredArticles = [];
        this.moreArticles = [];
        this.currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: '',
            source: '',
            sentiment: ''
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNews();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('news-search-input');
        const searchButton = document.getElementById('search-button');
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // Advanced search toggle
        const toggleAdvanced = document.getElementById('toggle-advanced-search');
        const advancedFilters = document.getElementById('advanced-filters');
        
        if (toggleAdvanced && advancedFilters) {
            toggleAdvanced.addEventListener('click', () => {
                advancedFilters.classList.toggle('show');
                const icon = toggleAdvanced.querySelector('i');
                if (advancedFilters.classList.contains('show')) {
                    icon.className = 'bi bi-chevron-up';
                } else {
                    icon.className = 'bi bi-sliders';
                }
            });
        }

        // Filter controls
        const applyFiltersBtn = document.getElementById('apply-filters');
        const clearFiltersBtn = document.getElementById('clear-filters');
        
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }
        
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    }

    async loadNews() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/news?limit=50');
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load news');
            }
            
            this.allArticles = data.all || [];
            this.processArticles();
            this.renderNews();
            
        } catch (error) {
            console.error('Error loading news:', error);
            this.showError(error.message);
        }
    }

    processArticles() {
        // Sort articles by date (newest first)
        this.allArticles.sort((a, b) => new Date(b.date || b.publish_time) - new Date(a.date || a.publish_time));
        
        // Select featured articles (first 4 with images preferred)
        const articlesWithImages = this.allArticles.filter(article => article.image_url);
        const articlesWithoutImages = this.allArticles.filter(article => !article.image_url);
        
        this.featuredArticles = [
            ...articlesWithImages.slice(0, 3),
            ...articlesWithoutImages.slice(0, 4 - articlesWithImages.slice(0, 3).length)
        ].slice(0, 4);
        
        // Remaining articles for "More News"
        const featuredIds = new Set(this.featuredArticles.map(a => a.id));
        this.moreArticles = this.allArticles.filter(article => !featuredIds.has(article.id)).slice(0, 20);
    }

    renderNews() {
        this.renderFeaturedNews();
        this.renderMoreNews();
    }

    renderFeaturedNews() {
        const container = document.getElementById('featured-news-grid');
        if (!container) return;

        if (this.featuredArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No featured news available</div>';
            return;
        }

        let html = '';
        this.featuredArticles.forEach(article => {
            const sentiment = this.getSentimentFromArticle(article);
            const sentimentClass = sentiment ? `sentiment-${sentiment.toLowerCase()}` : '';
            const sentimentBadge = sentiment ? `<span class="sentiment-badge ${sentimentClass}">${sentiment}</span>` : '';
            
            html += `
                <div class="featured-article" onclick="window.open('${article.url}', '_blank')">
                    ${article.image_url ? 
                        `<img src="${article.image_url}" alt="${article.title}" class="featured-article-image" onerror="this.style.display='none'">` :
                        `<div class="featured-article-image"></div>`
                    }
                    <div class="featured-article-content">
                        <h6 class="featured-article-title">${article.title}</h6>
                        <div class="featured-article-meta">
                            <span class="featured-article-source">${article.source}</span>
                            <span>${this.formatDate(article.date || article.publish_time)}</span>
                            ${sentimentBadge}
                        </div>
                        ${article.content ? 
                            `<p class="featured-article-summary">${article.content.substring(0, 150)}...</p>` : 
                            ''
                        }
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    renderMoreNews() {
        const container = document.getElementById('more-news-list');
        if (!container) return;

        if (this.moreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        let html = '';
        this.moreArticles.forEach(article => {
            const sentiment = this.getSentimentFromArticle(article);
            const sentimentBadge = sentiment ? `<span class="sentiment-badge sentiment-${sentiment.toLowerCase()}">${sentiment}</span>` : '';
            
            html += `
                <div class="news-list-item" onclick="window.open('${article.url}', '_blank')">
                    ${article.image_url ? 
                        `<img src="${article.image_url}" alt="${article.title}" class="news-list-image" onerror="this.style.display='none'">` :
                        `<div class="news-list-image"></div>`
                    }
                    <div class="news-list-content">
                        <h6 class="news-list-title">${article.title}</h6>
                        <div class="news-list-meta">
                            <span class="news-list-source">${article.source}</span>
                            <span>•</span>
                            <span>${this.formatDate(article.date || article.publish_time)}</span>
                            ${sentimentBadge}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    performSearch() {
        const searchInput = document.getElementById('news-search-input');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.applyFilters();
        }
    }

    applyFilters() {
        // Get filter values
        this.currentFilters.dateFrom = document.getElementById('date-from')?.value || '';
        this.currentFilters.dateTo = document.getElementById('date-to')?.value || '';
        this.currentFilters.source = document.getElementById('news-source')?.value || '';
        this.currentFilters.sentiment = document.getElementById('sentiment-filter')?.value || '';

        // Build API parameters
        const params = new URLSearchParams();
        if (this.currentFilters.search) params.append('search', this.currentFilters.search);
        if (this.currentFilters.dateFrom) params.append('start_date', this.currentFilters.dateFrom);
        if (this.currentFilters.dateTo) params.append('end_date', this.currentFilters.dateTo);
        params.append('limit', '50');

        // Fetch filtered news
        this.loadFilteredNews(params);
    }

    async loadFilteredNews(params) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load filtered news');
            }
            
            this.allArticles = data.all || [];
            
            // Apply client-side filters
            this.allArticles = this.allArticles.filter(article => {
                // Source filter
                if (this.currentFilters.source && article.source.toLowerCase() !== this.currentFilters.source.toLowerCase()) {
                    return false;
                }
                
                // Sentiment filter
                if (this.currentFilters.sentiment) {
                    const sentiment = this.getSentimentFromArticle(article);
                    if (!sentiment || sentiment.toLowerCase() !== this.currentFilters.sentiment.toLowerCase()) {
                        return false;
                    }
                }
                
                return true;
            });
            
            this.processArticles();
            this.renderNews();
            
        } catch (error) {
            console.error('Error loading filtered news:', error);
            this.showError(error.message);
        }
    }

    clearFilters() {
        // Clear all filter inputs
        document.getElementById('news-search-input').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';
        document.getElementById('news-source').value = '';
        document.getElementById('sentiment-filter').value = '';
        
        // Reset filters and reload
        this.currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: '',
            source: '',
            sentiment: ''
        };
        
        this.loadNews();
    }

    getSentimentFromArticle(article) {
        if (article.article_metadata && article.article_metadata.sentiment_analysis) {
            return article.article_metadata.sentiment_analysis.label;
        }
        return null;
    }

    formatDate(dateString) {
        if (!dateString) return 'Recent';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return '1 day ago';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
        
        return date.toLocaleDateString();
    }

    showLoading() {
        const featuredContainer = document.getElementById('featured-news-grid');
        const moreContainer = document.getElementById('more-news-list');
        
        const loadingHtml = `
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading news...</p>
            </div>
        `;
        
        if (featuredContainer) featuredContainer.innerHTML = loadingHtml;
        if (moreContainer) moreContainer.innerHTML = loadingHtml;
    }

    showError(message) {
        const featuredContainer = document.getElementById('featured-news-grid');
        const moreContainer = document.getElementById('more-news-list');
        
        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading news: ${message}
            </div>
        `;
        
        if (featuredContainer) featuredContainer.innerHTML = errorHtml;
        if (moreContainer) moreContainer.innerHTML = errorHtml;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new EnhancedNews();
});
