/**
 * Market Analysis JavaScript Module
 * Handles interactive charts and AI analysis display
 */

class MarketAnalysis {
    constructor() {
        this.currentTicker = 'SPY';
        this.currentApiProvider = 'gemini';
        this.chartData = null;
        this.analysisData = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Ticker selection
        const tickerSelect = document.getElementById('ticker-select');
        if (tickerSelect) {
            tickerSelect.addEventListener('change', (e) => {
                this.currentTicker = e.target.value;
                this.loadChartData();
            });
        }

        // Load analysis button
        const loadAnalysisBtn = document.getElementById('load-analysis-btn');
        if (loadAnalysisBtn) {
            loadAnalysisBtn.addEventListener('click', () => {
                this.loadAnalysis();
            });
        }

        // API provider selection
        const apiRadios = document.querySelectorAll('input[name="api-options"]');
        apiRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentApiProvider = e.target.value;
                this.loadAnalysis();
            });
        });
    }

    async loadInitialData() {
        await this.loadChartData();
        await this.loadAnalysis();
    }

    async loadChartData() {
        try {
            this.showChartLoading();

            // Get date range (last 6 months)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 6);

            const params = new URLSearchParams({
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            });

            // Use SP500 API for all tickers for now (in real implementation, would use ticker-specific APIs)
            const response = await fetch(`/api/sp500?${params.toString()}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load chart data');
            }

            this.chartData = data;
            this.renderChart(data);

        } catch (error) {
            console.error('Error loading chart data:', error);
            this.showChartError(error.message);
        }
    }

    async loadAnalysis() {
        try {
            this.showAnalysisLoading();
            
            const response = await fetch(`/api/llm-prediction?api=${this.currentApiProvider}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load AI analysis');
            }

            this.analysisData = data;
            this.renderAnalysis(data);

        } catch (error) {
            console.error('Error loading AI analysis:', error);
            this.showAnalysisError(error.message);
        }
    }

    showChartLoading() {
        const chartContainer = document.getElementById('price-chart');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading chart...</span>
                    </div>
                    <p class="mt-2">Loading ${this.currentTicker} price chart...</p>
                </div>
            `;
        }
    }

    showChartError(message) {
        const chartContainer = document.getElementById('price-chart');
        if (chartContainer) {
            chartContainer.innerHTML = `
                <div class="error-message">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Error loading chart:</strong> ${message}
                </div>
            `;
        }
    }

    showAnalysisLoading() {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (analysisContainer) {
            analysisContainer.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading AI analysis...</span>
                    </div>
                    <p class="mt-2">Generating AI market analysis with ${this.currentApiProvider}...</p>
                </div>
            `;
        }
    }

    showAnalysisError(message) {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (analysisContainer) {
            analysisContainer.innerHTML = `
                <div class="error-message">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Error loading AI analysis:</strong> ${message}
                </div>
            `;
        }
    }

    renderChart(data) {
        const chartContainer = document.getElementById('price-chart');
        if (!chartContainer || !data.prices) {
            this.showChartError('No chart data available');
            return;
        }

        // Prepare data for Plotly
        const dates = data.prices.map(item => item.date);
        const prices = data.prices.map(item => item.close);
        const volumes = data.prices.map(item => item.volume);

        const trace1 = {
            x: dates,
            y: prices,
            type: 'scatter',
            mode: 'lines',
            name: `${this.currentTicker} Price`,
            line: {
                color: '#007bff',
                width: 2
            },
            hovertemplate: '<b>%{fullData.name}</b><br>' +
                          'Date: %{x}<br>' +
                          'Price: $%{y:.2f}<br>' +
                          '<extra></extra>'
        };

        const layout = {
            title: {
                text: `${this.currentTicker} Price Chart`,
                font: { size: 16 }
            },
            xaxis: {
                title: 'Date',
                type: 'date'
            },
            yaxis: {
                title: 'Price ($)',
                tickformat: '$.2f'
            },
            hovermode: 'x unified',
            showlegend: true,
            margin: { t: 50, r: 50, b: 50, l: 60 },
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartContainer, [trace1], layout, config);
    }

    renderAnalysis(data) {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (!analysisContainer) return;

        let html = '';

        // Main prediction and confidence
        if (data.prediction && data.confidence) {
            const confidencePercent = (data.confidence * 100).toFixed(1);
            const predictionClass = data.prediction === 'up' ? 'text-success' : 
                                  data.prediction === 'down' ? 'text-danger' : 'text-warning';
            
            html += `
                <div class="confidence-display">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Market Direction Prediction</h6>
                            <h4 class="${predictionClass}">
                                <i class="bi bi-arrow-${data.prediction === 'up' ? 'up' : data.prediction === 'down' ? 'down' : 'right'}"></i>
                                ${data.prediction.toUpperCase()}
                            </h4>
                        </div>
                        <div class="col-md-6">
                            <h6>Overall Confidence</h6>
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: ${confidencePercent}%" 
                                     aria-valuenow="${confidencePercent}" aria-valuemin="0" aria-valuemax="100">
                                    ${confidencePercent}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Probabilities
        if (data.probabilities) {
            html += `
                <div class="row mb-3">
                    <div class="col-12">
                        <h6>Probability Breakdown</h6>
                        <div class="row">
            `;
            
            Object.entries(data.probabilities).forEach(([label, prob]) => {
                const percentage = (prob * 100).toFixed(1);
                const colorClass = label === 'positive' ? 'success' : 
                                 label === 'negative' ? 'danger' : 'warning';
                
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-${colorClass} mb-1">${label.charAt(0).toUpperCase() + label.slice(1)}</h6>
                                <h5 class="mb-0">${percentage}%</h5>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += `
                        </div>
                    </div>
                </div>
            `;
        }

        // Key Evidence
        if (data.key_evidence && data.key_evidence.length > 0) {
            html += `
                <div class="evidence-container">
                    <h6><i class="bi bi-search"></i> Key Evidence</h6>
            `;

            data.key_evidence.forEach(evidence => {
                const evidenceText = evidence.fact || evidence.text || evidence;
                html += `
                    <div class="evidence-item">
                        <div class="evidence-fact">${typeof evidenceText === 'string' ? evidenceText : JSON.stringify(evidenceText)}</div>
                `;

                if (evidence.article_title && evidence.source) {
                    html += `
                        <div class="article-reference">
                            <i class="bi bi-newspaper"></i>
                            <strong>${evidence.source}:</strong>
                            ${evidence.url ?
                                `<a href="${evidence.url}" target="_blank">${evidence.article_title}</a>` :
                                evidence.article_title
                            }
                        </div>
                    `;
                } else if (evidence.source) {
                    html += `
                        <div class="article-reference">
                            <i class="bi bi-newspaper"></i>
                            <strong>Source:</strong> ${evidence.source}
                        </div>
                    `;
                }

                html += `</div>`;
            });

            html += `</div>`;
        }

        // Dominant Theme
        if (data.dominant_theme && data.dominant_theme.theme) {
            html += `
                <div class="themes-section">
                    <h6><i class="bi bi-lightbulb"></i> Dominant Market Theme</h6>
                    <p class="mb-0">${data.dominant_theme.theme}</p>
                </div>
            `;
        }

        // Technical Indicators / Critical Levels
        if (data.critical_levels && Object.keys(data.critical_levels).length > 0) {
            html += `
                <div class="technical-indicators">
                    <h6><i class="bi bi-graph-up-arrow"></i> Critical Levels</h6>
                    <div class="row">
            `;
            
            Object.entries(data.critical_levels).forEach(([key, value]) => {
                if (typeof value === 'number') {
                    html += `
                        <div class="col-md-4 mb-2">
                            <strong>${key.charAt(0).toUpperCase() + key.slice(1)}:</strong> $${value.toFixed(2)}
                        </div>
                    `;
                } else if (typeof value === 'string') {
                    html += `
                        <div class="col-12 mb-2">
                            <strong>${key.charAt(0).toUpperCase() + key.slice(1)}:</strong> ${value}
                        </div>
                    `;
                }
            });
            
            html += `
                    </div>
                </div>
            `;
        }

        // Detailed Outlook Section
        if (data.detailed_outlook || data.outlook) {
            const outlook = data.detailed_outlook || data.outlook;
            html += `
                <div class="detailed-outlook-section">
                    <h6><i class="bi bi-eye"></i> Detailed Market Outlook</h6>
                    <div class="outlook-content">
                        ${typeof outlook === 'string' ? outlook : JSON.stringify(outlook, null, 2)}
                    </div>
                    <button class="btn btn-outline-info btn-sm mt-2" onclick="window.marketAnalysis.toggleDetailedOutlook()">
                        <i class="bi bi-chevron-down"></i> <span id="outlook-toggle-text">Show Full Outlook</span>
                    </button>
                </div>
            `;
        }

        // Metadata
        if (data.metadata) {
            html += `
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        Analysis generated using ${data.metadata.api || 'AI'}
                        ${data.metadata.model ? `(${data.metadata.model})` : ''}
                        at ${new Date(data.metadata.timestamp).toLocaleString()}
                    </small>
                </div>
            `;
        }

        analysisContainer.innerHTML = html;
    }

    toggleDetailedOutlook() {
        const outlookContent = document.querySelector('.outlook-content');
        const toggleText = document.getElementById('outlook-toggle-text');
        const toggleIcon = document.querySelector('.detailed-outlook-section .bi-chevron-down, .detailed-outlook-section .bi-chevron-up');

        if (outlookContent) {
            if (outlookContent.style.maxHeight === 'none') {
                outlookContent.style.maxHeight = '100px';
                outlookContent.style.overflow = 'hidden';
                toggleText.textContent = 'Show Full Outlook';
                toggleIcon.className = 'bi bi-chevron-down';
            } else {
                outlookContent.style.maxHeight = 'none';
                outlookContent.style.overflow = 'visible';
                toggleText.textContent = 'Hide Full Outlook';
                toggleIcon.className = 'bi bi-chevron-up';
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketAnalysis = new MarketAnalysis();
});
