/**
 * Market Overview JavaScript Module
 * Handles the market overview banner with real-time price data
 */

class MarketOverview {
    constructor() {
        this.tickers = ['SPY', 'VOO', '^GSPC', '^DJI', '^IXIC']; // SPY, VOO, S&P 500, Dow Jones, Nasdaq
        this.priceData = {};
        this.updateInterval = null;
        
        this.init();
    }

    init() {
        this.loadMarketData();
        // Update every 5 minutes during market hours
        this.updateInterval = setInterval(() => {
            this.loadMarketData();
        }, 5 * 60 * 1000);
    }

    async loadMarketData() {
        try {
            // For now, we'll use the existing SP500 API and simulate other data
            // In a real implementation, you would call multiple APIs or a unified market data API
            
            const response = await fetch('/api/sp500');
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load market data');
            }
            
            // Process SP500/SPY data
            if (data.prices && data.prices.length > 0) {
                const latestPrice = data.prices[data.prices.length - 1];
                const previousPrice = data.prices.length > 1 ? data.prices[data.prices.length - 2] : latestPrice;
                
                this.priceData['SPY'] = {
                    price: latestPrice.close,
                    change: latestPrice.close - previousPrice.close,
                    changePercent: ((latestPrice.close - previousPrice.close) / previousPrice.close) * 100
                };
                
                // Simulate other market data based on SPY (in real implementation, fetch actual data)
                this.simulateOtherMarketData(this.priceData['SPY']);
            }
            
            this.updateDisplay();
            
        } catch (error) {
            console.error('Error loading market data:', error);
            this.showError();
        }
    }

    simulateOtherMarketData(spyData) {
        // Simulate S&P 500 Index (multiply SPY by ~10 for index value)
        this.priceData['^GSPC'] = {
            price: spyData.price * 10.2, // Approximate multiplier
            change: spyData.change * 10.2,
            changePercent: spyData.changePercent
        };
        
        // Simulate Dow Jones (different multiplier and slight variation)
        const dowVariation = (Math.random() - 0.5) * 0.2; // ±0.1% variation
        this.priceData['^DJI'] = {
            price: spyData.price * 75 + (Math.random() * 100), // Approximate Dow level
            change: spyData.change * 75 * (1 + dowVariation),
            changePercent: spyData.changePercent + dowVariation
        };
        
        // Simulate Nasdaq (tech-heavy, more volatile)
        const nasdaqVariation = (Math.random() - 0.5) * 0.5; // ±0.25% variation
        this.priceData['^IXIC'] = {
            price: spyData.price * 32 + (Math.random() * 200), // Approximate Nasdaq level
            change: spyData.change * 32 * (1 + nasdaqVariation),
            changePercent: spyData.changePercent + nasdaqVariation
        };
        
        // Simulate VOO (similar to SPY but slightly different)
        const vooVariation = (Math.random() - 0.5) * 0.1; // ±0.05% variation
        this.priceData['VOO'] = {
            price: spyData.price * 1.02 + vooVariation, // VOO is slightly different from SPY
            change: spyData.change * 1.02 * (1 + vooVariation * 0.1),
            changePercent: spyData.changePercent + vooVariation * 0.1
        };
    }

    updateDisplay() {
        // Update S&P 500
        this.updateMetric('sp500', '^GSPC', 'S&P 500');
        
        // Update Dow Jones
        this.updateMetric('dow', '^DJI', 'Dow Jones');
        
        // Update Nasdaq
        this.updateMetric('nasdaq', '^IXIC', 'Nasdaq');
        
        // Update SPY ETF
        this.updateMetric('spy', 'SPY', 'SPY ETF');
        
        // Update VOO ETF
        this.updateMetric('voo', 'VOO', 'VOO ETF');
    }

    updateMetric(elementPrefix, ticker, displayName) {
        const data = this.priceData[ticker];
        if (!data) return;
        
        const priceElement = document.getElementById(`${elementPrefix}-price`);
        const changeElement = document.getElementById(`${elementPrefix}-change`);
        
        if (priceElement) {
            if (ticker.startsWith('^')) {
                // Index values (no dollar sign, show as whole numbers)
                priceElement.textContent = data.price.toFixed(0);
            } else {
                // ETF prices (show with dollar sign and 2 decimals)
                priceElement.textContent = `$${data.price.toFixed(2)}`;
            }
        }
        
        if (changeElement) {
            const changeText = `${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePercent >= 0 ? '+' : ''}${data.changePercent.toFixed(2)}%)`;
            changeElement.textContent = changeText;
            
            // Update color based on change
            changeElement.className = `metric-change ${data.change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    showError() {
        // Show error state for all metrics
        const metrics = ['sp500', 'dow', 'nasdaq', 'spy', 'voo'];
        
        metrics.forEach(metric => {
            const priceElement = document.getElementById(`${metric}-price`);
            const changeElement = document.getElementById(`${metric}-change`);
            
            if (priceElement) priceElement.textContent = 'Error';
            if (changeElement) {
                changeElement.textContent = 'Unable to load';
                changeElement.className = 'metric-change';
            }
        });
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketOverview = new MarketOverview();
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.marketOverview) {
        window.marketOverview.destroy();
    }
});
