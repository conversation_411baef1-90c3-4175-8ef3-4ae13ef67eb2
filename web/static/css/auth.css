/* Authentication Styles */

/* Auth Card */
.auth-card {
    background: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #212529;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-header h2 i {
    color: #007bff;
    margin-right: 0.5rem;
}

.auth-header p {
    margin-bottom: 0;
}

/* Auth Form */
.auth-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Password Input Group */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    z-index: 10;
}

.password-toggle:hover {
    color: #007bff;
}

/* Password Strength Meter */
.password-strength-meter {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.password-strength-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.password-strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-fill.weak {
    background-color: #dc3545;
    width: 25%;
}

.password-strength-fill.fair {
    background-color: #fd7e14;
    width: 50%;
}

.password-strength-fill.good {
    background-color: #ffc107;
    width: 75%;
}

.password-strength-fill.strong {
    background-color: #28a745;
    width: 100%;
}

.password-strength-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Auth Button */
.btn-auth {
    width: 100%;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-auth:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Auth Links */
.auth-links {
    text-align: center;
}

.auth-link {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #007bff;
    text-decoration: underline;
}

.auth-link-primary {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.auth-link-primary:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Profile Styles */
.profile-sidebar {
    background: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-avatar {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.profile-avatar i {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.profile-avatar h5 {
    margin-bottom: 0.5rem;
    color: #212529;
    font-weight: 600;
}

.profile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.profile-nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.profile-nav-link i {
    margin-right: 0.75rem;
    width: 1.25rem;
}

.profile-nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
    text-decoration: none;
}

.profile-nav-link.active {
    background-color: #007bff;
    color: #ffffff;
}

.profile-nav-link.active:hover {
    background-color: #0056b3;
    color: #ffffff;
}

/* Profile Card */
.profile-card {
    background: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-card-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.profile-card-header h4 {
    color: #212529;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.profile-card-header h4 i {
    color: #007bff;
    margin-right: 0.5rem;
}

/* Profile Tabs */
.profile-tab {
    display: none;
}

.profile-tab.active {
    display: block;
}

/* Profile Form */
.profile-form .form-group {
    margin-bottom: 1.5rem;
}

/* Email Preferences */
.email-preference-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.email-preference-group h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.form-check.form-switch {
    margin-bottom: 1rem;
    padding-left: 3rem;
}

.form-check-input {
    width: 2rem;
    height: 1rem;
    margin-left: -3rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Security Settings */
.security-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.security-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.security-item-icon {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #007bff;
    color: #ffffff;
    border-radius: 50%;
    margin-right: 1rem;
}

.security-item-icon i {
    font-size: 1.25rem;
}

.security-item-content {
    flex-grow: 1;
}

.security-item-content h6 {
    margin-bottom: 0.25rem;
    color: #212529;
    font-weight: 600;
}

.security-item-content p {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.security-item-action {
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .profile-sidebar {
        margin-bottom: 1rem;
    }
    
    .profile-nav {
        flex-direction: row;
        overflow-x: auto;
        gap: 0.25rem;
    }
    
    .profile-nav-link {
        white-space: nowrap;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .security-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .security-item-icon {
        margin-right: 0;
    }
}

@media (max-width: 576px) {
    .auth-card {
        padding: 1rem;
        border-radius: 0.75rem;
    }
    
    .profile-card {
        padding: 1.5rem;
        border-radius: 0.75rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .email-preference-group {
        padding: 1rem;
    }
}
