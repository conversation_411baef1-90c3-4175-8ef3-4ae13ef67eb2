/* Custom styles for the Market Monitor */

html {
    height: 100%;
    overflow-y: scroll;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    /* Ensure the page scrolls properly */
    height: auto;
    min-height: 100%;
    overflow-y: auto;
    position: relative;
}

.navbar-brand {
    font-weight: bold;
}

/* S&P 500 Chart Styles */
#sp500-graph {
    width: 100%;
    height: 350px;
    position: relative;
    overflow: visible;
}

/* Price summary styles */
#price-summary {
    padding-bottom: 10px;
    margin-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#current-price {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

#price-change {
    font-weight: 500;
}

.price-up {
    color: #28a745;
}

.price-down {
    color: #dc3545;
}

#price-summary .row .col-3 {
    padding: 8px;
}

#price-summary .small {
    font-size: 0.8rem;
    margin-bottom: 4px;
}

/* Chart time period buttons */
.btn-group .btn-outline-primary {
    border-radius: 0;
    font-weight: 500;
}

.btn-group .btn-outline-primary:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.btn-group .btn-outline-primary:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.news-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.5s ease;
}

.news-item:last-child {
    border-bottom: none;
}

/* Styling for new articles */
.news-item-new {
    background-color: rgba(40, 167, 69, 0.05);
    border-radius: 0.25rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(40, 167, 69, 0.1);
}

/* Animation duration adjustments */
.animate__animated.animate__fadeIn {
    --animate-duration: 1.5s;
}

.animate__animated.animate__fadeInDown {
    --animate-duration: 1s;
}

.news-source {
    font-size: 0.85rem;
    color: #6c757d;
}

.news-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.news-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.news-title-link {
    color: #212529;
    text-decoration: none;
    transition: color 0.2s ease;
}

.news-title-link:hover {
    color: #007bff;
    text-decoration: none;
}

.news-author {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

.news-source {
    font-size: 0.8rem;
    font-weight: normal;
}

.news-content {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #212529;
}

.news-link {
    display: inline-block;
    margin-top: 0.5rem;
}

.date-marker {
    cursor: pointer;
}

.date-marker:hover {
    fill: #007bff;
}

.btn-outline-primary.active {
    background-color: #007bff;
    color: white;
}

#loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.news-category {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    background-color: #e9ecef;
    color: #495057;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.news-summary {
    font-style: italic;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

/* Prediction styles */
#prediction-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

#prediction-container .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
}

#prediction-container .card-header {
    background-color: #007bff;
    color: white;
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

#prediction-container .card-body {
    flex: 1;
    display: flex;
    padding: 0.5rem;
    align-items: center;
    justify-content: center;
    min-height: 80px;
}

#prediction-container .progress {
    height: 0.5rem;
}

#prediction-container .progress-bar {
    font-weight: bold;
    font-size: 0.7rem;
}

#prediction-container .h5 {
    font-size: 1.15rem;
    margin-bottom: 0;
    line-height: 1.2;
}

#prediction-container .h6 {
    font-size: 1rem;
    margin-bottom: 0;
    line-height: 1.2;
}

/* Equal height cards */
.row.mb-4 {
    display: flex;
    flex-wrap: wrap;
}

.row.mb-4>[class*='col-'] {
    display: flex;
    flex-direction: column;
}

.row.mb-4 .card {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.row.mb-4 .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: visible;
}

/* Ensure the graph container takes up available space */
.card-body .flex-grow-1 {
    position: relative;
    min-height: 300px;
    overflow: visible;
}

/* Notification styles */
#notification-container {
    z-index: 1050;
}

#notification-container .toast {
    max-width: 350px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#notification-container .toast-header {
    font-weight: bold;
}

/* Animation for refreshed content */
@keyframes highlight-background {
    0% {
        background-color: rgba(40, 167, 69, 0.2);
    }

    100% {
        background-color: transparent;
    }
}

.highlight-refresh {
    animation: highlight-background 2s ease-out;
}

.tooltip .tooltip-inner {
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    border: 1px solid #ccc;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    max-width: 200px;
}

/* Highlight styles for matched keywords */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.1em 0.2em;
    border-radius: 2px;
    font-weight: 500;
}

.news-title mark {
    background-color: #fff3cd;
    color: #856404;
}

.news-summary mark {
    background-color: #fff3cd;
    color: #856404;
}

/* LLM Prediction Section Styles */
.prediction-section {
    transition: all 0.3s ease-in-out;
}

.prediction-section.mb-4 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1.5rem;
}

/* LLM Prediction Specific Styles */
#llm-prediction-section .card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

#llm-prediction-section .card {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

#llm-prediction-section .card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

/* LLM Section Headers */
#llm-prediction-section h6 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#llm-prediction-section h6 i {
    color: #17a2b8;
    font-size: 1.1em;
}

/* LLM Prediction Main Display */
#llm-prediction-main {
    font-size: 1.25rem;
    font-weight: 700;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    text-align: center;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

/* LLM Confidence Display */
#llm-prediction-confidence {
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1rem;
}

/* API Badge Styles */
#llm-api-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Prediction Type Toggle Styles */
.btn-check:checked+.btn-outline-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Progress Bar Enhancements */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Evidence List Styles */
.list-unstyled li {
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.75rem;
    transition: all 0.2s ease;
}

/* Enhanced Evidence Container Styles */
.evidence-container {
    /* Removed max-height and overflow-y for auto-height adjustment */
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.evidence-item {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.evidence-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #17a2b8 0%, #20c997 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    transform-origin: bottom;
}

.evidence-item.evidence-animate {
    opacity: 1;
    transform: translateY(0);
}

.evidence-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #17a2b8;
}

.evidence-item:hover::before {
    transform: scaleY(1);
}

.evidence-content {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    gap: 0.75rem;
}

.evidence-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    font-size: 0.75rem;
    margin-top: 0.125rem;
}

.evidence-text {
    flex: 1;
    min-width: 0;
}

.evidence-fact {
    font-size: 0.875rem;
    line-height: 1.5;
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.evidence-source {
    margin-top: 0.5rem;
}

.evidence-link {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    color: #17a2b8;
    text-decoration: none;
    font-size: 0.8125rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background-color: rgba(23, 162, 184, 0.1);
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.evidence-link:hover {
    color: #138496;
    background-color: rgba(23, 162, 184, 0.15);
    border-color: rgba(23, 162, 184, 0.2);
    text-decoration: none;
    transform: translateY(-1px);
}

.evidence-link i {
    font-size: 0.875rem;
}

/* Empty State Styles */
.evidence-empty-state {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    text-align: left;
}

.empty-state-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    font-size: 1.25rem;
}

.empty-state-text {
    flex: 1;
}

.empty-state-title {
    font-size: 0.9375rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.empty-state-subtitle {
    font-size: 0.8125rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Theme Container Styles */
.theme-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.theme-container:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.theme-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.theme-header i {
    font-size: 1rem;
}

.theme-content {
    padding: 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #2c3e50;
    border-bottom: 1px solid #f1f3f4;
}

.theme-articles {
    padding: 1rem;
}

.theme-articles-header {
    font-weight: 600;
    font-size: 0.8125rem;
    color: #495057;
    margin-bottom: 0.75rem;
}

.theme-article-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border-left: 3px solid #17a2b8;
}

.theme-article-link {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    color: #17a2b8;
    text-decoration: none;
    font-size: 0.8125rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.theme-article-link:hover {
    color: #138496;
    text-decoration: none;
}

.theme-article-relevance {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
    font-style: italic;
}

/* Theme Empty State */
.theme-empty-state {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 0.5rem;
    text-align: left;
}

.theme-empty-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    font-size: 1.125rem;
}

.theme-empty-text {
    flex: 1;
}

.theme-empty-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #856404;
    margin-bottom: 0.25rem;
}

.theme-empty-subtitle {
    font-size: 0.8125rem;
    color: #856404;
    line-height: 1.4;
    opacity: 0.8;
}

/* Compact spacing for LLM prediction sections */
#llm-prediction-section .alert {
    margin-bottom: 0.75rem;
}

#llm-prediction-section .card-body>div:last-child {
    margin-bottom: 0;
}

.list-unstyled li:hover {
    border-left-color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

/* Modal Enhancements */
.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.modal-title i {
    font-size: 1.2em;
}

/* Card Border Enhancements */
.card.border-primary {
    border-width: 2px !important;
}

.card.border-success {
    border-width: 2px !important;
}

.card.border-danger {
    border-width: 2px !important;
}

.card.border-warning {
    border-width: 2px !important;
}

.card.border-info {
    border-width: 2px !important;
}

/* Loading Animation */
@keyframes pulse-info {
    0% {
        box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(23, 162, 184, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(23, 162, 184, 0);
    }
}

.spinner-border-sm.text-info {
    animation: pulse-info 2s infinite;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    #llm-api-select {
        width: 100% !important;
        margin-bottom: 0.5rem;
    }

    .card-header .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }

    .prediction-section .row {
        flex-direction: column;
    }

    .prediction-section .col-md-4,
    .prediction-section .col-md-8 {
        margin-bottom: 1rem;
    }

    /* Evidence responsive styles */
    .evidence-content {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .evidence-fact {
        font-size: 0.8125rem;
    }

    .evidence-link {
        font-size: 0.75rem;
        padding: 0.1875rem 0.375rem;
    }

    .evidence-empty-state {
        padding: 1.5rem 1rem;
        gap: 0.75rem;
        flex-direction: column;
        text-align: center;
    }

    .empty-state-icon {
        width: 40px;
        height: 40px;
        font-size: 1.125rem;
        align-self: center;
    }

    /* Theme responsive styles */
    .theme-empty-state {
        flex-direction: column;
        text-align: center;
        padding: 1.25rem 1rem;
        gap: 0.75rem;
    }

    .theme-empty-icon {
        align-self: center;
    }

    .theme-header {
        padding: 0.625rem 0.75rem;
        font-size: 0.8125rem;
    }

    .theme-content {
        padding: 0.75rem;
        font-size: 0.8125rem;
    }

    .theme-articles {
        padding: 0.75rem;
    }

    .theme-article-link {
        font-size: 0.75rem;
    }
}

/* Alert Enhancements */
.alert-light {
    background-color: rgba(248, 249, 250, 0.8);
    border-color: rgba(222, 226, 230, 0.8);
}

.border-start {
    border-left-width: 4px !important;
}

/* Badge Enhancements */
.badge.fs-6 {
    font-size: 0.875rem !important;
}

/* Button Hover Effects */
.btn-outline-info:hover,
.btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

/* Icon Spacing */
.bi {
    margin-right: 0.25rem;
}

/* Critical Levels Card Enhancements */
.card-header.bg-success,
.card-header.bg-danger,
.card-header.bg-primary {
    font-weight: 600;
}

.card-body h4 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}