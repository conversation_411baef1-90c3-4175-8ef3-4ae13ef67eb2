{% extends "layout.html" %}

{% block title %}Market Analysis - Market Monitor{% endblock %}

{% block head %}
<!-- Additional CSS for Market Analysis -->
<style>
    .market-analysis-container {
        padding: 20px 0;
    }
    
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .ticker-selector {
        margin-bottom: 20px;
    }
    
    .ai-analysis-section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .prediction-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .confidence-display {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .evidence-container {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        max-height: none;
        overflow: visible;
    }
    
    .evidence-item {
        background: white;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 10px;
        border-left: 4px solid #007bff;
    }
    
    .evidence-item:last-child {
        margin-bottom: 0;
    }
    
    .article-reference {
        font-size: 0.9em;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .article-reference a {
        color: #007bff;
        text-decoration: none;
    }
    
    .article-reference a:hover {
        text-decoration: underline;
    }
    
    .themes-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .technical-indicators {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .loading-spinner {
        text-align: center;
        padding: 40px;
    }
    
    .error-message {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container market-analysis-container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-graph-up"></i> Market Analysis
                <small class="text-muted">- Advanced AI-Powered Insights</small>
            </h1>
        </div>
    </div>

    <!-- Ticker Selector -->
    <div class="row">
        <div class="col-12">
            <div class="ticker-selector">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Select Ticker</h5>
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <select class="form-select" id="ticker-select">
                                    <option value="SPY" selected>SPY - SPDR S&P 500 ETF</option>
                                    <option value="VOO">VOO - Vanguard S&P 500 ETF</option>
                                    <option value="QQQ">QQQ - Invesco QQQ Trust</option>
                                    <option value="IWM">IWM - iShares Russell 2000 ETF</option>
                                    <option value="DIA">DIA - SPDR Dow Jones Industrial Average ETF</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-primary" id="load-analysis-btn">
                                    <i class="bi bi-arrow-clockwise"></i> Load Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Chart -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">Price Chart</h5>
                <div id="price-chart" style="height: 400px;">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading chart...</span>
                        </div>
                        <p class="mt-2">Loading price chart...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Analysis Section -->
    <div class="row">
        <div class="col-12">
            <div class="ai-analysis-section">
                <div class="prediction-header">
                    <h5><i class="bi bi-robot"></i> AI Market Analysis</h5>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="api-options" id="api-gemini" value="gemini" checked>
                        <label class="btn btn-outline-primary" for="api-gemini">Gemini</label>
                        
                        <input type="radio" class="btn-check" name="api-options" id="api-openai" value="openai">
                        <label class="btn btn-outline-primary" for="api-openai">OpenAI</label>
                        
                        <input type="radio" class="btn-check" name="api-options" id="api-anthropic" value="anthropic">
                        <label class="btn btn-outline-primary" for="api-anthropic">Anthropic</label>
                    </div>
                </div>

                <div id="ai-analysis-content">
                    <div class="loading-spinner">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading AI analysis...</span>
                        </div>
                        <p class="mt-2">Generating AI market analysis...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/market-analysis.js') }}"></script>
{% endblock %}
