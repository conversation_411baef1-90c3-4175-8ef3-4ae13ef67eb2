{% extends "layout.html" %}

{% block title %}Register - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="auth-card">
            <div class="auth-header">
                <h2><i class="bi bi-person-plus"></i> Create Account</h2>
                <p class="text-muted">Join Market Monitor to get personalized news and insights</p>
            </div>

            <form method="POST" class="auth-form" action="{{ url_for('auth.register') }}" id="registration-form">
                {{ form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.first_name.label(class="form-label") }}
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else ""),
                            placeholder="Enter first name", id="first_name") }}
                            {% if form.first_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.first_name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.last_name.label(class="form-label") }}
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else ""),
                            placeholder="Enter last name", id="last_name") }}
                            {% if form.last_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.last_name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""),
                    placeholder="Choose a username", id="username") }}
                    <div class="form-text">Username must be 3-80 characters long and unique.</div>
                    {% if form.username.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.username.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""),
                    placeholder="Enter email address", id="email") }}
                    <div class="form-text">We'll send you important updates and daily summaries.</div>
                    {% if form.email.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.email.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""),
                        placeholder="Create password", id="password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="bi bi-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    <div class="form-text">
                        Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                    </div>
                    {% if form.password.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.password_confirm.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors
                        else ""), placeholder="Confirm password", id="password_confirm") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('password_confirm')">
                            <i class="bi bi-eye" id="password_confirm-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password_confirm.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password_confirm.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div class="password-strength-meter">
                    <div class="password-strength-bar">
                        <div class="password-strength-fill" id="password-strength-fill"></div>
                    </div>
                    <div class="password-strength-text" id="password-strength-text">Password strength</div>
                </div>

                <button type="submit" class="btn btn-primary btn-auth">
                    <i class="bi bi-person-plus"></i> Create Account
                </button>
            </form>

            <div class="auth-links">
                <hr>
                <p class="text-center">
                    Already have an account?
                    <a href="{{ url_for('auth.login') }}" class="auth-link-primary">Login here</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}