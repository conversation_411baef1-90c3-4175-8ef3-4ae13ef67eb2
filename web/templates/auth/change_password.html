{% extends "layout.html" %}

{% block title %}Change Password - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="auth-card">
            <div class="auth-header">
                <h2><i class="bi bi-key"></i> Change Password</h2>
                <p class="text-muted">Update your account password</p>
            </div>
            
            <form method="POST" class="auth-form">
                {{ form.hidden_tag() }}
                
                <div class="form-group">
                    {{ form.current_password.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else ""), placeholder="Enter current password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                            <i class="bi bi-eye" id="current_password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.current_password.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.current_password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.new_password.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else ""), placeholder="Enter new password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                            <i class="bi bi-eye" id="new_password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.new_password.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.new_password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.new_password_confirm.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.new_password_confirm(class="form-control" + (" is-invalid" if form.new_password_confirm.errors else ""), placeholder="Confirm new password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('new_password_confirm')">
                            <i class="bi bi-eye" id="new_password_confirm-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.new_password_confirm.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.new_password_confirm.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="password-strength-meter">
                    <div class="password-strength-bar">
                        <div class="password-strength-fill" id="password-strength-fill"></div>
                    </div>
                    <div class="password-strength-text" id="password-strength-text">Password strength</div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-auth">
                    <i class="bi bi-check-circle"></i> Change Password
                </button>
            </form>
            
            <div class="auth-links">
                <hr>
                <p class="text-center">
                    <a href="{{ url_for('auth.profile') }}" class="auth-link">Back to Profile</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
<script>
// Monitor new password for strength
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    if (newPasswordField) {
        newPasswordField.addEventListener('input', function() {
            updatePasswordStrength(this.value);
        });
    }
});
</script>
{% endblock %}
