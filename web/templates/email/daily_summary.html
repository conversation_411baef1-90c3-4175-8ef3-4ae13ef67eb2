<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Market Summary</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            max-width: 650px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .email-container {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 30px;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f59e0b, #ef4444, #8b5cf6);
        }

        .header h1 {
            margin: 0 0 8px 0;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        .header .greeting {
            font-size: 18px;
            margin: 12px 0 0 0;
            font-weight: 500;
        }

        .content {
            padding: 0;
        }

        .section {
            margin: 0;
            padding: 32px 30px;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
        }

        .section:last-of-type {
            border-bottom: none;
        }

        .section-title {
            color: #1e293b;
            margin: 0 0 24px 0;
            font-size: 22px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title .emoji {
            font-size: 24px;
            display: inline-block;
        }

        /* Market Overview Styles */
        .market-overview {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .market-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 16px;
            margin: 0;
        }

        .metric {
            background: white;
            padding: 20px 16px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease;
        }

        .metric:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .metric-label {
            font-size: 11px;
            color: #64748b;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .metric-change {
            font-size: 12px;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 20px;
            display: inline-block;
        }

        .positive {
            color: #059669;
            background: #d1fae5;
        }

        .negative {
            color: #dc2626;
            background: #fee2e2;
        }

        /* Predictions Styles */
        .predictions-section {
            background: #fafafa;
        }

        .prediction-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .prediction-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .prediction-header {
            padding: 24px 20px 0 20px;
            text-align: center;
        }

        .prediction-type {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .traditional-model .prediction-type {
            color: #3b82f6;
        }

        .ai-analysis .prediction-type {
            color: #10b981;
        }

        .prediction-main-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .traditional-model .prediction-main-value {
            color: #1e40af;
        }

        .ai-analysis .prediction-main-value {
            color: #047857;
        }

        .confidence-section {
            margin-bottom: 16px;
        }

        .confidence-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
        }

        .confidence-bar {
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .confidence-fill {
            height: 100%;
            border-radius: 3px;
        }

        .traditional-model .confidence-fill {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }

        .ai-analysis .confidence-fill {
            background: linear-gradient(90deg, #10b981, #047857);
        }

        .prediction-details {
            padding: 0 20px 24px 20px;
        }

        .probabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            margin-bottom: 16px;
        }

        .probability-item {
            text-align: center;
            background: #f8fafc;
            padding: 12px 8px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .probability-label {
            font-size: 11px;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 4px;
        }

        .probability-value {
            font-size: 14px;
            font-weight: 700;
            color: #1e293b;
        }

        .evidence-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #10b981;
        }

        .evidence-title {
            font-size: 13px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .evidence-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .evidence-item {
            font-size: 13px;
            color: #4b5563;
            line-height: 1.5;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .evidence-item::before {
            content: '•';
            color: #10b981;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .evidence-item:last-child {
            margin-bottom: 0;
        }

        .market-theme {
            background: linear-gradient(135deg, #fef3c7, #fed7aa);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f59e0b;
            margin-top: 16px;
        }

        .theme-title {
            font-size: 13px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .theme-content {
            font-size: 14px;
            color: #78350f;
            font-weight: 500;
        }

        /* News Styles */
        .news-section {
            background: white;
        }

        .news-grid {
            display: grid;
            gap: 16px;
        }

        .news-item {
            background: #fafafa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .news-item:hover {
            background: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .news-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 15px;
            line-height: 1.4;
        }

        .news-meta {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .news-summary {
            font-size: 14px;
            color: #4b5563;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            padding: 40px 30px;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Footer */
        .footer {
            background: #f8fafc;
            text-align: center;
            padding: 30px;
            color: #64748b;
            font-size: 13px;
            border-top: 1px solid #e2e8f0;
        }

        .footer a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .footer-links {
            margin: 16px 0;
        }

        .footer-links a {
            margin: 0 8px;
        }

        /* Error State */
        .error-section {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            border: 1px solid #f87171;
            color: #b91c1c;
            text-align: center;
            padding: 32px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .error-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .error-message {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Mobile Responsiveness */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 26px;
            }

            .section {
                padding: 24px 20px;
            }

            .section-title {
                font-size: 20px;
            }

            .market-summary {
                grid-template-columns: repeat(2, 1fr);
            }

            .prediction-container {
                grid-template-columns: 1fr;
            }

            .probabilities-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .cta-section {
                padding: 30px 20px;
            }
        }

        @media (max-width: 400px) {
            .market-summary {
                grid-template-columns: 1fr;
            }

            .probabilities-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <h1>📈 Market Monitor</h1>
            <p class="subtitle">Daily Market Summary for {{ data.date.strftime('%B %d, %Y') }}</p>
            <p class="greeting">Hello {{ user.first_name or user.username }}!</p>
        </div>

        <div class="content">
            {% if data.error %}
            <div class="section error-section">
                <div class="error-icon">⚠️</div>
                <div class="error-title">Service Temporarily Unavailable</div>
                <div class="error-message">
                    We encountered an issue generating your daily summary. Please visit our website for the latest
                    market information.
                </div>
            </div>
            {% else %}

            <!-- Market Summary Section -->
            {% if data.market_data %}
            <div class="section market-overview">
                <h2 class="section-title">
                    <span class="emoji">📊</span>
                    Market Overview
                </h2>
                <div class="market-summary">
                    <div class="metric">
                        <div class="metric-label">Current Price</div>
                        <div class="metric-value">${{ "%.2f"|format(data.market_data.current_price or 0) }}</div>
                        {% if data.market_data.price_change %}
                        <div
                            class="metric-change {{ 'positive' if data.market_data.price_change > 0 else 'negative' }}">
                            {{ "%.2f"|format(data.market_data.price_change) }} ({{
                            "%.2f"|format(data.market_data.price_change_percent) }}%)
                        </div>
                        {% endif %}
                    </div>
                    <div class="metric">
                        <div class="metric-label">Volume</div>
                        <div class="metric-value">{{ "{:,}"|format(data.market_data.volume or 0) }}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Day High</div>
                        <div class="metric-value">${{ "%.2f"|format(data.market_data.high or 0) }}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Day Low</div>
                        <div class="metric-value">${{ "%.2f"|format(data.market_data.low or 0) }}</div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Predictions Section -->
            {% if data.prediction or data.llm_prediction %}
            <div class="section predictions-section">
                <h2 class="section-title">
                    <span class="emoji">🔮</span>
                    Market Predictions
                </h2>

                <div class="prediction-container">
                    {% if data.prediction %}
                    <div class="prediction-card traditional-model">
                        <div class="prediction-header">
                            <div class="prediction-type">Traditional Model</div>
                            <div class="prediction-main-value">{{ data.prediction.prediction_label or 'N/A' }}</div>

                            {% if data.prediction.confidence %}
                            <div class="confidence-section">
                                <div class="confidence-label">
                                    <span>Confidence</span>
                                    <span>{{ "%.1f"|format((data.prediction.confidence or 0) * 100) }}%</span>
                                </div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: {{ "
                                        %.1f"|format((data.prediction.confidence or 0) * 100) }}%"></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if data.llm_prediction %}
                    <div class="prediction-card ai-analysis">
                        <div class="prediction-header">
                            <div class="prediction-type">AI Analysis</div>
                            <div class="prediction-main-value">{{ data.llm_prediction.prediction or 'N/A' }}</div>

                            {% if data.llm_prediction.confidence %}
                            <div class="confidence-section">
                                <div class="confidence-label">
                                    <span>Overall Confidence</span>
                                    <span>{{ "%.1f"|format(data.llm_prediction.confidence * 100) }}%</span>
                                </div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: {{ "
                                        %.1f"|format(data.llm_prediction.confidence * 100) }}%"></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <div class="prediction-details">
                            {% if data.llm_prediction.probabilities %}
                            <div class="probabilities-grid">
                                {% for label, score in data.llm_prediction.probabilities.items() %}
                                <div class="probability-item">
                                    <div class="probability-label">{{ label }}</div>
                                    <div class="probability-value">{{ "%.1f"|format(score * 100) }}%</div>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            {% if data.llm_prediction.key_evidence and data.llm_prediction.key_evidence|length > 0 %}
                            <div class="evidence-section">
                                <div class="evidence-title">
                                    <span>🔍</span>
                                    Key Evidence
                                </div>
                                <ul class="evidence-list">
                                    {% for evidence in data.llm_prediction.key_evidence[:3] %}
                                    <li class="evidence-item">
                                        {{ evidence.fact[:100] }}{% if evidence.fact|length > 100 %}...{% endif %}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}

                            {% if data.llm_prediction.dominant_theme and data.llm_prediction.dominant_theme.theme %}
                            <div class="market-theme">
                                <div class="theme-title">
                                    <span>📊</span>
                                    Dominant Market Theme
                                </div>
                                <div class="theme-content">
                                    {{ data.llm_prediction.dominant_theme.theme }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Top News Section -->
            {% if data.news and data.news.all %}
            <div class="section news-section">
                <h2 class="section-title">
                    <span class="emoji">📰</span>
                    Top Financial News
                </h2>
                <div class="news-grid">
                    {% for article in data.news.all[:5] %}
                    <div class="news-item">
                        <div class="news-title">{{ article.title }}</div>
                        <div class="news-meta">
                            {{ article.source }} • {{ article.date.strftime('%B %d, %Y') if article.date else 'Recent'
                            }}
                        </div>
                        {% if article.content %}
                        <div class="news-summary">
                            {{ article.content[:150] }}{% if article.content|length > 150 %}...{% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            {% endif %}
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <a href="{{ url_for('index', _external=True) }}" class="cta-button">
                View Full Dashboard
            </a>
        </div>

        <div class="footer">
            <p>
                This email was sent to {{ user.email }} because you subscribed to daily market summaries.
            </p>
            <div class="footer-links">
                <a href="{{ url_for('auth.profile', _external=True) }}">Update Preferences</a>
                <a href="{{ url_for('auth.profile', _external=True) }}">Unsubscribe</a>
            </div>
            <p>
                © {{ data.date.year }} Market Monitor. All rights reserved.
            </p>
        </div>
    </div>
</body>

</html>