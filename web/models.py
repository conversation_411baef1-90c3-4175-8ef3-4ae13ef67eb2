"""
Flask-SQLAlchemy models for the NewsMonitor web application.

This module provides User and related models that work properly with Flask-SQLAlchemy
and Flask-Login, resolving detached instance issues.
"""

from typing import Dict
from sqlalchemy import Boolean, Column, String, Text, DateTime, ForeignKey, Integer, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

from web.database import db


class User(db.Model, UserMixin):
    """User model for authentication and user management."""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)

    # Profile information
    first_name = Column(String(50))
    last_name = Column(String(50))

    # Account status - renamed to avoid conflict with UserMixin.is_active property
    active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False)
    updated_at = Column(DateTime(timezone=True), nullable=False)
    last_login = Column(DateTime(timezone=True))

    # Email preferences
    email_preferences = Column(JSONB, default=lambda: {
        'daily_summary': True,
        'market_alerts': True,
        'news_digest': True,
        'frequency': 'daily'  # daily, weekly, never
    })

    # User preferences
    user_preferences = Column(JSONB, default=lambda: {
        'theme': 'light',
        'timezone': 'UTC',
        'language': 'en',
        'news_sources': [],
        'watchlist': []
    })

    # Relationships
    email_logs = relationship(
        "EmailLog", back_populates="user", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index('ix_users_email_active', 'email', 'active'),
        Index('ix_users_username_active', 'username', 'active'),
        Index('ix_users_created_at', 'created_at'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<User(username={self.username!r}, email={self.email!r})>"

    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        """Return user ID as string for Flask-Login."""
        return str(self.id)

    @property
    def is_active(self):
        """Flask-Login required property - maps to our 'active' column."""
        return self.active

    @property
    def is_authenticated(self):
        """Flask-Login required property."""
        return True

    @property
    def is_anonymous(self):
        """Flask-Login required property."""
        return False

    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary."""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'active': self.active,
            'is_verified': self.is_verified,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_login': self.last_login,
            'email_preferences': self.email_preferences,
            'user_preferences': self.user_preferences
        }

        if include_sensitive:
            data['password_hash'] = self.password_hash

        return data


class EmailLog(db.Model):
    """Email log model for tracking sent emails."""
    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey(
        'users.id', ondelete='CASCADE'), nullable=False)

    # Email details
    # daily_summary, market_alert, etc.
    email_type = Column(String(50), nullable=False)
    subject = Column(String(255), nullable=False)
    recipient_email = Column(String(120), nullable=False)

    # Status and metadata
    status = Column(String(20), default='pending',
                    nullable=False)  # pending, sent, failed
    sent_at = Column(DateTime(timezone=True))
    error_message = Column(Text)

    # Content metadata
    content_hash = Column(String(64))  # For deduplication
    template_version = Column(String(20))

    # Relationships
    user = relationship("User", back_populates="email_logs")

    # Indexes for performance
    __table_args__ = (
        Index('ix_email_logs_user_id', 'user_id'),
        Index('ix_email_logs_status', 'status'),
        Index('ix_email_logs_sent_at', 'sent_at'),
        Index('ix_email_logs_email_type', 'email_type'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<EmailLog(user_id={self.user_id}, type={self.email_type}, status={self.status})>"

    def to_dict(self):
        """Convert email log to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'email_type': self.email_type,
            'subject': self.subject,
            'recipient_email': self.recipient_email,
            'status': self.status,
            'sent_at': self.sent_at,
            'error_message': self.error_message,
            'content_hash': self.content_hash,
            'template_version': self.template_version
        }
